import axios, { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import Cookies from "js-cookie";
import {
  ACCESS_TOKEN,
  BUSINESS_UID,
  FIRST_NAME,
  LAST_NAME,
  REFRESH_TOKEN,
  USERNAME,
} from "../constants/Constant";

//  Environment-based URLs
const OMS_BASE_URL = import.meta.env.VITE_API_URL;     // OMS  
const SOK_BASE_URL = import.meta.env.VITE_API_URL_SOK;  // SOK

// Common config
const commonConfig = {
  timeout: 30000,
  headers: { "Content-Type": "application/json" },
};

// 🔹 Utility to attach headers dynamically
const attachHeaders = (config: InternalAxiosRequestConfig) => {
  const token = Cookies.get(ACCESS_TOKEN);
  const businessUid = Cookies.get(BUSINESS_UID);

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  if (businessUid) {
    config.headers["X-Business-Uid"] = businessUid;
  }
  return config;
};

// 🔹 Response Refresh Token Handling
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

const onRefreshed = (token: string) => {
  refreshSubscribers.forEach((callback) => callback(token));
  refreshSubscribers = [];
};

const handleResponseError = async (error: any, instance: AxiosInstance) => {
  const originalRequest = error.config;
  const isAuthEndpoint =
    originalRequest.url.includes("/login") ||
    originalRequest.url.includes("/token/refresh");

  if (
    error.response?.status === 401 &&
    !originalRequest._retry &&
    !isAuthEndpoint
  ) {
    originalRequest._retry = true;

    if (!isRefreshing) {
      isRefreshing = true;
      try {
        const { data } = await axios.post(
          `${OMS_BASE_URL}/api/token/refresh/`,
          { refresh: Cookies.get(REFRESH_TOKEN) },
          { headers: { "Content-Type": "application/json" } }
        );

        Cookies.set(ACCESS_TOKEN, data.access, { secure: true, sameSite: "Strict" });
        Cookies.set(REFRESH_TOKEN, data.refresh, { secure: true, sameSite: "Strict" });

        isRefreshing = false;
        onRefreshed(data.access);

        originalRequest.headers.Authorization = `Bearer ${data.access}`;
        return instance(originalRequest);
      } catch (err) {
        isRefreshing = false;
        [ACCESS_TOKEN, REFRESH_TOKEN, FIRST_NAME, LAST_NAME, USERNAME, BUSINESS_UID].forEach((key) =>
          Cookies.remove(key)
        );
        window.location.href = "/";
        return Promise.reject(err);
      }
    }

    return new Promise((resolve) => {
      refreshSubscribers.push((token: string) => {
        originalRequest.headers.Authorization = `Bearer ${token}`;
        resolve(instance(originalRequest));
      });
    });
  }

  return Promise.reject(error);
};

// 🔹 Create Axios Instances
const createInstance = (baseURL: string): AxiosInstance => {
  const instance = axios.create({ baseURL, ...commonConfig });

  instance.interceptors.request.use(
    (config) => attachHeaders(config),
    (error) => Promise.reject(error)
  );

  instance.interceptors.response.use(
    (response) => response,
    (error) => handleResponseError(error, instance)
  );

  return instance;
};


export const axiosOMS = createInstance(OMS_BASE_URL);
export const axiosInstance = createInstance(SOK_BASE_URL);
