import React, { useState, useEffect, useRef, useMemo } from "react";
import { Input, InputRef, Spin } from "antd";
import { axiosInstance } from "../../apiCalls";
import { KIOSKS } from "../../constants/Constant";
import { KioskType } from "../../types/KioskType/KioskType";
import { kioskIs_Active_Options } from "../Store-mapping/Kiosk/SelectTypeKiosk/SelectTypeKioksList";
import { Link } from "react-router-dom";
import dayjs from "dayjs";
import { SearchOutlined } from "@ant-design/icons";
import StoreFilterDropdown from "../Orders/OrderFilters/StoreNameFilter";
import { FilterDropdownProps } from "antd/es/table/interface";
import { handleApiError } from "../../utils/ApiErrorHandler";
import FilterButtons from "../shared/FilterButton/FilterButton";
import useMetaData from "../../hooks/useMetaData";
import { useTableFilters } from "../../hooks/useTableFilter";
import FilterMenu from "../shared/FilterMenu";
import ErrorFallback from "../Error/ErrorPage";
import DataTable from "../shared/DataTable/commonDataTable";
import "../../assets/css/Kiosk/KioskList.css";
import CommonPagination from "../shared/Pagination/commonPagination";

export interface KioskResponse {
  objects: KioskType[];
  total_count: number;
}

interface StatusOption {
  label: string;
  value: string | number;
}

const KioskMasterList: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    setFilters,
    appliedFilters,
    showClearButtons,
    updateURLParams,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();
  const { metaData, errors } = useMetaData();
  const [kiosks, setKiosks] = useState<KioskType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);

  const codeInputRef = useRef<InputRef>(null);

  const [dynamicAttributes, setDynamicAttributes] = useState({
    kiosk_status_types: [],
  });

  useEffect(() => {
    // const controller = new AbortController();

    // const fetchAttributes = async () => {
    //   try {
    //     const response = await axiosInstance.get("/pos/orders/meta-data/", {
    //       signal: controller.signal, // Attach signal to abort request
    //     });

    //     if (response.status === 200) {
    //       setDynamicAttributes(response.data.kiosk_meta);
    //     }
    //   } catch (error: unknown) {
    //     handleApiError(error, setError);
    //   }
    // };

    // fetchAttributes();

    // return () => {
    //   controller.abort(); //
    // };

    const kioskMeta = metaData?.kiosk_meta;
    // console.log(kioskMeta);
    if (kioskMeta) {
      setDynamicAttributes(kioskMeta);
    }
  }, [metaData, errors]);

  const memoizedFilters = useMemo(() => filters, [filters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchKiosks = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<KioskResponse>(
          "/cms/stores/kiosks/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setKiosks(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: unknown) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchKiosks();
    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  // Remove a single filter and update the URL
  // const clearFilter = (key: string) => {
  //   const updatedFilters = { ...filters };
  //   delete updatedFilters[key];

  //   setFilters(updatedFilters);

  //   // Update URL
  //   const params = new URLSearchParams();
  //   Object.entries(updatedFilters).forEach(([k, v]) => {
  //     if (v) params.set(k, v);
  //   });

  //   navigate(params.toString() ? `?${params.toString()}` : location.pathname);
  // };

  // // Clear all filters and reset URL
  // const clearAllFilters = () => {
  //   setFilters({});

  //   navigate(location.pathname); // Reset URL
  // };

  // text formatter
  const TextFormat = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };
  // const formatDate = (dateString: string): string => {
  //   return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  // };

  // Function to capitalize the first letter of a string
  const capitalizeFirstLetter = (text: string) => {
    if (!text) return text;
    return text.charAt(0).toUpperCase() + text.slice(1);
  };

  // Function to get the filter menu for a specific filter key
  // const getFilterMenu = (
  //   setSelectedKeys: (keys: string[]) => void,
  //   selectedKeys: React.Key[],
  //   confirm: () => void,
  //   filterKey: string,
  //   options: { label: string; value: string | number }[]
  // ) => {
  //   return (
  //     <Menu
  //       onClick={({ key }) => {
  //         const stringKey = String(key);
  //         setSelectedKeys(stringKey ? [stringKey] : []);
  //         confirm();
  //         handleFilterChange(filterKey, stringKey);
  //       }}
  //       selectedKeys={selectedKeys.map((key) => String(key))}
  //       items={[
  //         { key: "All", label: "All" },
  //         ...options
  //           .filter((option) => option.value !== "All")
  //           .map((option) => ({
  //             key: String(option.value),
  //             label: option.label,
  //           })),
  //       ]}
  //     />
  //   );
  // };

  const columns: any = [
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: "10%",
      fixed: "left" as "left",
      filteredValue: filters.code ? [filters.code] : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            className="filter-input-code"
            ref={codeInputRef}
            placeholder="Search code"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("code", "All");
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("code", selectedKeys[0]);
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  className="search-filter-clear-btn"
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("code", "All");
                  }}
                  style={{ cursor: "pointer", color: "#999" }}
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
          }
        },
      },
      render: (text: string, record: KioskType) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/store-kiosks-details/${record.store_id}/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    {
      title: "Store",
      dataIndex: "store",
      key: "store",
      width: "15%",
      filteredValue: filters.store ? ([filters.store] as string[]) : null,
      filterDropdown: useMemo(
        () =>
          ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
          }: FilterDropdownProps) =>
            (
              <StoreFilterDropdown
                selectedKeys={selectedKeys as string[]}
                setSelectedKeys={setSelectedKeys}
                confirm={confirm}
                clearFilters={clearFilters ?? (() => {})}
                handleFilterChange={handleFilterChange}
                inputRef={codeInputRef}
                setFilters={setFilters}
                updateURLParams={updateURLParams}
                filters={filters}
              />
            ),
        [filters.store]
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100);
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
      render: (text: string, record: KioskType) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.store_id}/details`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    {
      title: "Token",
      dataIndex: "token",
      key: "token",
      width: "20%",
      filteredValue: filters.token ? [filters.token] : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Search token"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("token", "All");
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("token", selectedKeys[0]);
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("token", "All");
                  }}
                  style={{ cursor: "pointer", color: "#999" }}
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100);
          }
        },
      },
    },
    // {
    //   title: "Printer Serial Number",
    //   dataIndex: "printer_serial_number",
    //   key: "printer_serial_number",
    //   width: "20%",
    //   filteredValue: filters.printer_serial_number
    //     ? [filters.printer_serial_number]
    //     : null,
    //   filterDropdown: ({
    //     setSelectedKeys,
    //     selectedKeys,
    //     confirm,
    //     clearFilters,
    //   }: any) => (
    //     <div className="filter-dropdown" style={{ padding: 8 }}>
    //       <Input
    //         ref={codeInputRef}
    //         placeholder="Search printer serial number"
    //         value={selectedKeys[0]}
    //         onChange={(e) => {
    //           const value = e.target.value;
    //           setSelectedKeys(value ? [value] : []);
    //           if (!value) {
    //             clearFilters();
    //             confirm();
    //             handleFilterChange("printer_serial_number", "All");
    //           }
    //         }}
    //         onPressEnter={() => {
    //           confirm();
    //           handleFilterChange("edc_serial_number", selectedKeys[0]);
    //         }}
    //         autoFocus
    //         suffix={
    //           selectedKeys[0] ? (
    //             <span
    //               onClick={() => {
    //                 setSelectedKeys([]);
    //                 clearFilters();
    //                 confirm();
    //                 handleFilterChange("edc_serial_number", "All");
    //               }}
    //               style={{ cursor: "pointer", color: "#999" }}
    //             >
    //               ✖
    //             </span>
    //           ) : null
    //         }
    //       />
    //     </div>
    //   ),
    //   filterDropdownProps: {
    //     onOpenChange: (visible: boolean) => {
    //       if (visible) {
    //         setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
    //       }
    //     },
    //   },
    // },
    {
      title: "Date",
      dataIndex: "updated_at",
      key: "updated_at",
      width: "20%",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },
    {
      title: "Status",
      dataIndex: "type",
      key: "type",
      width: "15%",
      filteredValue: filters.type ? [filters.type] : null,
      filterDropdown: (props: any) => (
        <FilterMenu
          {...props}
          filterKey="type"
          options={dynamicAttributes.kiosk_status_types.map((status) => ({
            label: capitalizeFirstLetter(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (type: string) => capitalizeFirstLetter(type),
    },
    {
      title: "Active",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      filteredValue: filters.is_active ? [filters.is_active] : null,
      filterDropdown: (props: any) => (
        <FilterMenu
          {...props}
          filterKey="is_active"
          options={kioskIs_Active_Options.map((status: StatusOption) => ({
            label: TextFormat(status.label),
            value: status.value,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (isActive: boolean) => (isActive ? "Yes" : "No"),
    },
  ];

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div>
      {/* <Divider className="divider">Orders</Divider> */}
      <div className="heading-title">{KIOSKS}</div>

      <div className="pt-4">
        <FilterButtons
          showClearButtons={showClearButtons}
          appliedFilters={appliedFilters}
          clearAllFilters={clearAllFilters}
          clearFilter={clearFilter}
          formatFilterValue={TextFormat}
          filters={filters}
        />
      </div>

      {loading ? (
        <div className="loading-container">
          <Spin size="large" />
        </div>
      ) : (
        <div className="pt-2 mt-4">
          <DataTable
            columns={columns}
            dataSource={kiosks}
            pagination={false}
            rowKey="id"
            scroll={{ x: 1000 }}
          />
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            showSizeChanger
            onShowSizeChange={handlePageChange}
            onChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default KioskMasterList;
