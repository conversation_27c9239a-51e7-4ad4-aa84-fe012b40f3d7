#root {
  /* max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
     */
  width: 100%;
  height: 100%;
  /* text-align: center; */
  /* background-color: #f5f5f5; */
  color: #333;
  font-family: "Poppins", serif !important;
}

.search-result:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

/* back button */
.backButton {
  margin-bottom: 2;
  display: flex;
  align-items: center;
  background-color: #ff8732;
  color: #fff;
}
.backButton:hover {
  margin-bottom: 2;
  display: flex;
  align-items: center;
  background-color: #ff8732 !important;
  color: #fff !important;
  filter: drop-shadow(0 0 2em #ff8732aa);
}
.error-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.btn-back-home {
  background-color: #ff8732;
  color: #fff;
  border-color: #ff8732;
}

.btn-back-home:hover {
  background-color: #ff8732 !important;
  color: #fff !important;
  filter: drop-shadow(0 0 2em #ff8732aa); 
  border-color: #ff8732 !important;
}

@media screen and (max-width: 768px) {
  :root {
    overflow-x: auto;
    color: #213547;
    background-color: #ffffff;
  }
}
