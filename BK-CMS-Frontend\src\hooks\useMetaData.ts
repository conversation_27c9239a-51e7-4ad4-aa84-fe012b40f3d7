import { useState, useEffect } from "react";
import { axiosInstance } from "../apiCalls";
import { handleApiError } from "../utils/ApiErrorHandler";

const useMetaData = () => {
  const [metaData, setMetaData] = useState<any>(null);
  const [errors, setError] = useState<string | null>(null);
  const [isloading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const controller = new AbortController();

    const fetchAttributes = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get("/pos/orders/meta-data/", {
          signal: controller.signal,
        });

        if (response.status === 200) {
          setMetaData(response.data);
        }
      } catch (error: unknown) {
        handleApiError(error, setError);
      }
    };

    fetchAttributes();

    return () => {
      controller.abort();
    };
  }, []);

  return { metaData, errors, isloading };
};

export default useMetaData;
