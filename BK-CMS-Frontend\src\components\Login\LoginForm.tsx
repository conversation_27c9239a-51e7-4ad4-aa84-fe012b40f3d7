import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { LoginUserProps } from "../../types";
import Cookies from "js-cookie";
import { axiosInstance } from "../../apiCalls";
import { useNavigate } from "react-router-dom";
import { Container, Row, Col, Form, Button, Spinner } from "react-bootstrap";
import { useEffect, useState, useCallback } from "react";
import { message } from "antd"; // Use Ant Design message for better UX
import "../../assets/css/LoginForm/LoginForm.css";
import { handleApiError } from "../../utils/ApiErrorHandler";
import {
  ACCESS_TOKEN,
  FIRST_NAME,
  LAST_NAME,
  LOGIN_PATH,
  REFRESH_TOKEN,
  USERNAME,
} from "../../constants/Constant";

// Form validation schema
const loginFormSchema = z.object({
  username: z
    .string()
    .nonempty("Username is required")
    .min(2, "Username must be at least 2 characters long")
    .max(50, "Username must be at most 50 characters long"),
  password: z.string().nonempty("Password is required"),
});

const LoginForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [errorDisplayed, setErrorDisplayed] = useState(false);

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginUserProps>({
    resolver: zodResolver(loginFormSchema),
  });

  // Login form submission
  const onSubmit = useCallback(
    async (data: LoginUserProps) => {
      setLoading(true);
      setError(null); // Reset error on new login attempt
      setErrorDisplayed(false); // Reset error display on new login attempt
      try {
        const response = await axiosInstance.post("/cms/accounts/login/", data);

        if (response.status === 200) {
          Cookies.set(ACCESS_TOKEN, response.data.access, {
            secure: true,
            sameSite: "Strict",
          });
          Cookies.set(REFRESH_TOKEN, response.data.refresh, {
            secure: true,
            sameSite: "Strict",
          });
          Cookies.set(USERNAME, response.data.user?.username, {
            secure: true,
            sameSite: "Strict",
          });
          Cookies.set(FIRST_NAME, response.data.user?.first_name, {
            secure: true,
            sameSite: "Strict",
          });
          Cookies.set(LAST_NAME, response.data.user?.last_name, {
            secure: true,
            sameSite: "Strict",
          });

          navigate("/orders-list");
        }
      } catch (error: unknown) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    },
    [navigate]
  );

  // Auto-login if token exists
  useEffect(() => {
    const token = Cookies.get(ACCESS_TOKEN);

    // Only redirect if the user is already logged in and is not trying to access the login page
    if (token && window.location.pathname === LOGIN_PATH) {
      navigate("/orders-list");
    }
  }, [navigate]);

  useEffect(() => {
    if (error && !errorDisplayed) {
      message.error(error);
      setErrorDisplayed(true); // Prevent multiple messages
    }
  }, [error, errorDisplayed]);

  if (!loading && error) {
    message.error(error);
    setError(null);
  }

  return (
    <Container
      fluid
      className="d-flex justify-content-center align-items-center vh-100"
    >
      <Row className="w-100 justify-content-center px-3">
        <Col xs={12} sm={10} md={8} lg={6} xl={4}>
          <div className="p-4 border rounded shadow bg-white">
            <h2 className="text-center mb-4">Login</h2>
            <Form onSubmit={handleSubmit(onSubmit)}>
              <Form.Group controlId="username" className="mb-3">
                <Form.Label>Username</Form.Label>
                <Form.Control
                  type="text"
                  {...register("username")}
                  isInvalid={!!errors.username}
                />
                {errors.username && (
                  <Form.Control.Feedback type="invalid">
                    {errors.username.message}
                  </Form.Control.Feedback>
                )}
              </Form.Group>
              <Form.Group controlId="password" className="mb-3">
                <Form.Label>Password</Form.Label>
                <Form.Control
                  type="password"
                  {...register("password")}
                  isInvalid={!!errors.password}
                />
                {errors.password && (
                  <Form.Control.Feedback type="invalid">
                    {errors.password.message}
                  </Form.Control.Feedback>
                )}
              </Form.Group>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <a href="/forgot-password" className="text-primary">
                  Forgot Password?
                </a>
              </div>
              <div className="d-flex justify-content-center">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="btn-login w-100 font-poppins"
                >
                  {loading ? (
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                  ) : null}
                  <span>{loading ? "Logging in..." : "Login"}</span>
                </Button>
              </div>
            </Form>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default LoginForm;
