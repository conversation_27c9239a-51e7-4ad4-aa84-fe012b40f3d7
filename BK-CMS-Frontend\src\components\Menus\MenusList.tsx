import React, { useEffect, useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>, But<PERSON>, Pagination } from "antd";
import { MenuItem, MenuResponse } from "../../types";
import { axiosInstance } from "../../apiCalls";
import dayjs from "dayjs";
import { MENUS, PAGINATION_DEFAULTS, Retry } from "../../constants/Constant";
import { handleApiError } from "../../utils/ApiErrorHandler";
import { useLocation, useNavigate } from "react-router-dom";

const MenusList: React.FC = () => {
  const [menuData, setMenuData] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);

  const location = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(location.search);

  const initialPage =
    Number(searchParams.get("page")) || PAGINATION_DEFAULTS.PAGE;
  const initialPageSize =
    Number(searchParams.get("page_size")) || PAGINATION_DEFAULTS.PAGE_SIZE;

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [filters, setFilters] = useState<Record<string, string | undefined>>(
    () => {
      const filterObj: Record<string, string | undefined> = {};
      searchParams.forEach((value, key) => {
        if (!["page", "page_size"].includes(key)) {
          filterObj[key] = value;
        }
      });
      return filterObj;
    }
  );

  // Update URL params when filters or pagination changes
  const updateURLParams = (
    updatedFilters: Record<string, string | undefined>,
    page?: number,
    size?: number
  ) => {
    const params = new URLSearchParams();

    // Preserve filters
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });

    // Add pagination params
    params.set("page", String(page || currentPage));
    params.set("page_size", String(size || pageSize));

    navigate(`?${params.toString()}`);
  };

  // Fetch menu data with cleanup
  const fetchMenuData = useCallback(
    async (page: number, size: number) => {
      const controller = new AbortController();
      const signal = controller.signal;

      setLoading(true);
      setError(null);

      try {
        const response = await axiosInstance.get<MenuResponse>(
          "/cms/menu/menus/",
          {
            params: { page, page_size: size, ...filters },
            signal,
          }
        );

        if (response.status === 200) {
          setMenuData(response.data.objects);
          setTotalCount(response.data.total_count);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: unknown) {
        console.error("API Error:", error);
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }

      return () => controller.abort();
    },
    [filters]
  );

  // Fetch data when page or pageSize changes
  useEffect(() => {
    fetchMenuData(currentPage, pageSize);
  }, [currentPage, pageSize, fetchMenuData]);

  // Handle page change
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
      setCurrentPage(1); // Reset to first page when pageSize changes
    }
    const updatedFilters = {
      ...filters,
      page: page.toString(),
      page_size: size?.toString() || pageSize.toString(),
    };
    setFilters(updatedFilters);
    updateURLParams(filters, page, size || pageSize);
  };

  const columns = [
    // {
    //   title: "ID",
    //   dataIndex: "id",
    //   key: "id",
    //   width: "10%",
    //   fixed: "left" as "left",
    // },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
      fixed: "left" as "left",
    },
    {
      title: "Updated At",
      dataIndex: "updated_at",
      key: "updated_at",
      width: "15%",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },
    {
      title: "Active",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean) => (isActive ? "Yes" : "No"),
    },
  ];

  if (!loading && error) {
    return (
      <div className="error-container">
        <Alert message="Error" description={error} type="error" showIcon />
        <div className="retry-button d-flex justify-content-center align-items-center mt-3">
          <Button
            type="primary"
            onClick={() => fetchMenuData(currentPage, pageSize)}
          >
            {Retry}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <h4>{MENUS}</h4>
      </div>

      <div className="pt-4 mt-4">
        <Table<MenuItem>
          dataSource={menuData}
          columns={columns}
          loading={loading}
          pagination={false}
          rowKey="id"
        />
        <div className="pagination">
          <Pagination
            current={currentPage}
            total={totalCount}
            pageSize={pageSize}
            showSizeChanger={true}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  );
};

export default MenusList;
