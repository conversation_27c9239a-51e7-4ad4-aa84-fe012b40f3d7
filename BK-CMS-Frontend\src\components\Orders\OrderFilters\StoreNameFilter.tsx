import { Input, InputRef, Spin } from "antd";
import { useEffect, useRef, useState } from "react";
import axios from "axios";
import { axiosInstance } from "../../../apiCalls";

interface StoreType {
  id: string;
  store: string;
  code: string;
  name: string;
}
interface StoreFilterProps {
  setSelectedKeys: (keys: string[]) => void;
  selectedKeys: string[];
  confirm: () => void;
  clearFilters: () => void;
  handleFilterChange: (key: string, value: string) => void;
  inputRef: React.RefObject<InputRef>;
  setFilters: React.Dispatch<
    React.SetStateAction<Record<string, string | undefined>>
  >;
  updateURLParams: (updatedFilters: Record<string, string | undefined>) => void;
  filters: Record<string, string | undefined>;
}

const StoreFilterDropdown: React.FC<StoreFilterProps> = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  handleFilterChange,
  inputRef,
  setFilters,
  updateURLParams,
  filters,
}: any) => {
  const [options, setOptions] = useState<StoreType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [query, setQuery] = useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = useState<string>("");
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
  const [isNavigating, setIsNavigating] = useState(false); // Prevent API calls on arrow navigation

  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);

  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const controllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    if (inputRef?.current) {
      inputRef.current.focus({ cursor: "end" });
    }
  }, [selectedKeys]);

  useEffect(() => {
    if (isNavigating) return;

    debounceTimeout.current = setTimeout(() => {
      setDebouncedQuery(query);
    }, 500);

    return () => {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    };
  }, [query, isNavigating]);

  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setOptions([]);
      return;
    }

    if (controllerRef.current) {
      controllerRef.current.abort();
    }

    const controller = new AbortController();
    controllerRef.current = controller;

    setLoading(true);
    axiosInstance
      .get("/cms/stores/stores/", {
        params: {
          name: debouncedQuery,
          // page: "1",
          // page_size: "100",
        },
        signal: controller.signal,
      })
      .then((response) => {
        if (response.status === 200) {
          const filteredOptions = response.data.objects.filter((store: any) =>
            store.name.toLowerCase().startsWith(debouncedQuery.toLowerCase())
          );
          setOptions(filteredOptions || []);
          setHighlightedIndex(-1);
        } else {
          setOptions([]);
        }
      })
      .catch((error) => {
        if (!axios.isCancel(error))
          console.error("Error fetching store suggestions:", error);
      })
      .finally(() => setLoading(false));

    return () => controller.abort();
  }, [debouncedQuery]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (options.length === 0) return;

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setIsNavigating(true);
      setHighlightedIndex((prev) => {
        const newIndex = prev < options.length - 1 ? prev + 1 : 0;

        optionRefs.current[newIndex]?.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
        });
        return newIndex;
      });
    }

    if (e.key === "ArrowUp") {
      e.preventDefault();
      setIsNavigating(true);
      setHighlightedIndex((prev) => {
        const newIndex = prev > 0 ? prev - 1 : options.length - 1;
        optionRefs.current[newIndex]?.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
        });

        return newIndex;
      });
    }

    if (e.key === "Enter" && highlightedIndex !== -1) {
      const selectedOption = options[highlightedIndex];
      setQuery(selectedOption.name);
      setSelectedKeys([selectedOption.name]);
      confirm();
      handleFilterChange("store", selectedOption.name);
      setHighlightedIndex(-1);
      setIsNavigating(false);
    }
  };

  return (
    <>
      <div>
        <div className="filter-input-wrapper">
          <Input
            ref={inputRef}
            className="store-filter-input border-solid border-gray-300"
            placeholder="Enter Store Name"
            value={
              isNavigating && highlightedIndex !== -1
                ? options[highlightedIndex].name
                : query
            }
            onChange={(e) => {
              setQuery(e.target.value);
              setSelectedKeys(e.target.value ? [e.target.value] : []);
              setHighlightedIndex(-1);
              setIsNavigating(false);
            }}
            onFocus={() => {
              if (selectedKeys[0]) {
                setQuery(selectedKeys[0]);
              }
            }}
            onKeyDown={handleKeyDown}
            onPressEnter={() => {
              if (highlightedIndex === -1) {
                confirm();
                handleFilterChange("store", query || "");
              }
            }}
            suffix={
              <span
                className={`clear-icon ${query ? "" : "hidden"}`}
                onClick={() => {
                  setQuery("");
                  setSelectedKeys([]);
                  clearFilters();
                  confirm();
                  setHighlightedIndex(-1);
                  handleFilterChange("store", "All");
                  setOptions([]);
                }}
              >
                ✖
              </span>
            }
          />
        </div>
      </div>
      <div className="filter-options">
        {loading ? (
          <div className="d-flex justify-content-center align-items-center">
            <Spin size="small" />
          </div>
        ) : options.length > 0 ? (
          options.map((option: StoreType, index: number) => (
            <div
              ref={(el) => (optionRefs.current[index] = el)}
              key={option.id || `option-${index}`}
              className={`filter-option ${
                index === highlightedIndex ? "highlighted" : ""
              }`}
              onMouseEnter={() => setHighlightedIndex(index)}
              onClick={() => {
                setQuery(option.name);
                setSelectedKeys([option.name]);
                confirm();
                handleFilterChange("store", option.name);
                setHighlightedIndex(-1);
                setIsNavigating(false);
                setFilters((prevFilters: StoreType) => ({
                  ...prevFilters,
                  store: option.name,
                  page: "1", // Reset to first page
                }));

                updateURLParams({ ...filters, store: option.name, page: "1" });

                setHighlightedIndex(-1);
                setIsNavigating(false);
              }}
            >
              {option.name} ({option.code})
            </div>
          ))
        ) : (
          <div className="no-suggestions">No results found</div>
        )}
      </div>
    </>
  );
};

export default StoreFilterDropdown;
