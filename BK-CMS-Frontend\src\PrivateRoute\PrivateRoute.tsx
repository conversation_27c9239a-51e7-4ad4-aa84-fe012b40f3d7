import React from "react";
import { Navigate } from "react-router-dom";
import Cookies from "js-cookie";
import { PrivateRouteProps } from "../types/PrivateRouteProps";
import { ACCESS_TOKEN, LOGIN_PATH } from "../constants/Constant";

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const token = Cookies.get(ACCESS_TOKEN);

  if (!token) {
    // Redirect to login page if not authenticated
    return <Navigate to={LOGIN_PATH} />;
  }
  return <>{children}</>; // Render children (protected routes) if authenticated
};

export default PrivateRoute;
