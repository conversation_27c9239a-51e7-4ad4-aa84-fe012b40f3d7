import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Upload, Button, message, Card } from "antd";
import { UploadOutlined, DownloadOutlined } from "@ant-design/icons";
import {
  RcFile,
  UploadChangeParam,
  UploadFile,
} from "antd/es/upload/interface";
import { axiosInstance } from "../../apiCalls";
import { FILE_NAME, FILE_PATH } from "../../utils/filePath";
import {
  Cancel,
  No_File_Selected,
  Sample_CSV,
  Select_CSV_File,
  TITLE,
} from "../../constants/Constant";

const UploadCSV: React.FC = () => {
  const navigate = useNavigate();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [loading, setLoading] = useState(false);

  // Handle File Upload
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error("Please select a CSV file to upload.");
      return;
    }
    setLoading(true);
    const formData = new FormData();
    formData.append("file", fileList[0].originFileObj as RcFile);

    try {
      const response = await axiosInstance.post(
        "cms/stores/bulk_import/",
        formData,
        { headers: { "Content-Type": "multipart/form-data" } }
      );

      if (response.status === 200 || response.status === 201) {
        if (response.data?.success || response.data?.message) {
          message.success(
            response.data.message || "Stores imported successfully!"
          );
          navigate("/stores-list");
        } else {
          message.warning("Stores imported, but no confirmation received.");
        }
      } else {
        message.error(
          response.data?.error || "Upload failed. Please check the file format."
        );
      }
    } catch (error: any) {
      console.error("File upload error:", error);
      message.error(
        error.response?.data?.error || "An error occurred during file upload."
      );
    }
  };

  // Handle file selection
  const handleFileChange = (info: UploadChangeParam<UploadFile<any>>) => {
    // Check if the file list is empty before proceeding
    if (info.fileList.length === 0) {
      setFileList([]); // Ensure the file list is cleared properly
      return; // Stop execution if no file is selected
    }

    const file = info.fileList[0]?.originFileObj as RcFile;

    if (!file) {
      return; // Prevent unnecessary error message
    }

    if (file.type !== "text/csv") {
      message.error("Only CSV files are allowed.");
      return;
    }

    if (file.size === 0) {
      message.error("Empty files cannot be uploaded.");
      return;
    }

    setFileList(info.fileList);
  };

  // Handle Export CSV
  const handleExport = () => {
    try {
      if (!FILE_PATH) throw new Error("File path is undefined");

      const link = document.createElement("a");
      link.href = FILE_PATH;
      link.download = FILE_NAME;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success("CSV template downloaded successfully.");
    } catch (error) {
      console.error("Error exporting file:", error);
      message.error("Failed to download CSV template.");
    }
  };

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-left heading-title">
        <div>{TITLE}</div>
        <Button
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleExport}
          className="mb-2"
        >
          {Sample_CSV}
        </Button>
      </div>

      <Card
        title={TITLE}
        className="mx-auto mt-3"
        style={{ maxWidth: "600px" }}
      >
        <Upload
          beforeUpload={() => false}
          fileList={fileList}
          onChange={handleFileChange}
          onRemove={() => {
            setFileList([]); // Clear file list when the file is removed
            return true; // Allows file removal
          }}
          accept=".csv"
          maxCount={1}
        >
          <Button icon={<UploadOutlined />}>{Select_CSV_File}</Button>
        </Upload>

        {fileList.length === 0 && !loading && (
          <p className="text-danger mt-3">{No_File_Selected}</p>
        )}

        <div className="d-flex justify-content-center gap-2 mt-4">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleUpload}
            loading={loading}
          >
            {loading ? "Uploading..." : "Save"}
          </Button>

          <Button
            className="btn-cancel"
            onClick={() => navigate(`/stores-list`)}
          >
            {Cancel}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default UploadCSV;
