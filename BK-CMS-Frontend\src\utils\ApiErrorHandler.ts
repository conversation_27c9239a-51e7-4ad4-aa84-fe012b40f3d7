import axios from "axios";

const DEBUG = import.meta.env.MODE === "development";
// Enable logs only in development

/**
 * Handles API errors and sets user-friendly messages.
 * @param error - The error object from the API call.
 * @param setError - Function to update the error state.
 */
export const handleApiError = (
  error: unknown,
  setError: (message: string | null) => void
) => {
  if (axios.isCancel(error)) {
    DEBUG && console.warn("Request canceled:", (error as any).message);
    return;
  }

  if (!navigator.onLine) {
    setError("No internet connection. Please check your network.");
    return;
  }

  if (axios.isAxiosError(error) && error.response) {
    const { status, data } = error.response;

    const errorMessage =
      {
        401: "Unauthorized: Invalid credentials or session expired.",
        403: "Forbidden. You do not have permission to access this page.",
        404: "Page not found.",
        500: "A server error occurred. Please try again later.",
      }[status] ||
      data?.message ||
      "An unexpected error occurred.";

    setError(errorMessage);
    DEBUG && console.error(`API Error [${status}]:`, data);
    return;
  }

  if (axios.isAxiosError(error) && error.request) {
    setError("Network error. Unable to reach the server. Please try again.");
    DEBUG && console.error("Network Error:", error.request);
    return;
  }

  // Handle any other unknown errors
  setError((error as any)?.message || "An unexpected error occurred.");
  DEBUG && console.error("Unknown Error:", error);
};
