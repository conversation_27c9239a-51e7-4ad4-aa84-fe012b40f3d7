/* KioskList.css */

.table-container {
  overflow-x: auto !important;
}

.divider {
  font-weight: bold !important;
  font-size: 20px !important;
   background-color: #ff7f00;
}

.filter-dropdown {
  padding: 8px !important;
  font-family: "poppins", sans-serif !important;
  background: #fff !important;
  position: relative;
  width: 100%;
}
.filter-input {
  width: 180px !important;
}

.filter-button {
  margin-top: 8px !important;
  color: #fc8434 !important;
  width: 100px !important;
  border: 1px solid #fc8434 !important;
}
.filter-button:hover {
  background-color: #fc8434 !important;
  color: #fff !important;
  border: none !important;
}

/* .filter-input {
  width: 100px !important;
} */

.button-group {
  display: flex !important;
  justify-content: space-between !important;
  margin-top: 4px !important;
}

.button-group .ant-btn {
  width: 60px !important;
}
.add-banner-button {
  background: #ff7f00 !important;
}

.pagination {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: end;
  align-items: center;
}
