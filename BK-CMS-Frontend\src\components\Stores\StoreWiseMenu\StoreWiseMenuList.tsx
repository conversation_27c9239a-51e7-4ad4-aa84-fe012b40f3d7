import  { useState, useEffect } from "react";
import { Table, Divider, Button } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { MenuByStoreProps } from "../../../types/MenusType/MenuListByStore";
import { EditOutlined } from "@ant-design/icons";

interface MenuType {
  id: number;
  code: string;
  display_name: string;
  is_active: boolean;

}

const StoreWiseMenuList = ({ storeId }: MenuByStoreProps) => {
  const [products, setProducts] = useState<MenuType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(
        `/cms/menu/store-menus/?store_id=${storeId}`
      ); // Adjust API endpoint
      if (response.status === 200) {
        setProducts(response.data.objects);
      } else {
        setError(`Unexpected response status: ${response.status}`);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to fetch products");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);
  const handleEdit = (id: number) => {
    console.log(`Edit menu with ID: ${id}`);
  };

  const columns = [
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
    },
    {
      title: "Display Name",
      dataIndex: "display_name",
      key: "display_name",
    },
    {
      title: "Active",
      dataIndex: "is_active",
      key: "is_active",
      render: (isActive: boolean) => (isActive ? "Yes" : "No"),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: MenuType) => (
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record.id)}
        >
          Edit
        </Button>
      ),
    },
  ];

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  return (
    <div className="table-container">
      <Divider className="divider">Menu List</Divider>
      <Table<MenuType>
        bordered
        dataSource={products}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />
    </div>
  );
};

export default StoreWiseMenuList;
