import { useState, useEffect, useCallback } from "react";
import { Table, Button, Pagination, message, Modal, Alert } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import { StoreBannerProps } from "./TypeProps/StoreBannerTypeProps";
import { Object as BannerObject } from "../../../types/BannerType/BannerType";
import ImportBannerModal from "./ImportBanner";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { handleApiError } from "../../../utils/ApiErrorHandler";
import { Retry } from "../../../constants/Constant";

const StoreBanner = ({ storeId }: StoreBannerProps) => {
  const [banners, setBanners] = useState<BannerObject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [isImportModalVisible, setImportModalVisible] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();

  const params = new URLSearchParams(location.search);
  const initialPage = parseInt(params.get("page") || "1", 10);
  const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [filters, setFilters] = useState<Record<string, string | undefined>>(
    useCallback(() => {
      const filterObj: Record<string, string | undefined> = {};
      params.forEach((value, key) => {
        if (!["page", "page_size"].includes(key)) {
          filterObj[key] = value;
        }
      });
      return filterObj;
    }, [params])
  );

  const fetchBanners = useCallback(
    async (page = currentPage, size = pageSize, filtersParam = filters) => {
      try {
        setLoading(true);
        setError(null);

        const response = await axiosInstance.get("/cms/menu/store-banners/", {
          params: { store_id: storeId, page, page_size: size, ...filtersParam },
        });

        if (response.status === 200) {
          setBanners(response.data.objects);
          setTotalCount(response.data.total_count);
        } else {
          setError(`Unexpected response status: ${response.status}`);
        }
      } catch (err: unknown) {
        handleApiError(err, setError);
      } finally {
        setLoading(false);
      }
    },
    [storeId, currentPage, pageSize, filters]
  );

  useEffect(() => {
    fetchBanners();
  }, [fetchBanners]);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);

    const updatedFilters = {
      ...filters,
      page: page.toString(),
      page_size: (size || pageSize).toString(),
    };
    setFilters(updatedFilters);
    navigate(`?${new URLSearchParams(updatedFilters).toString()}`);
    fetchBanners(page, size || pageSize, updatedFilters);
  };

  // const handleFilterChange = (key: string, value: string) => {
  //   const updatedFilters =
  //     value === "All" ? { page: "1" } : { ...filters, [key]: value, page: "1" };
  //   setFilters(updatedFilters);
  //   navigate(`?${new URLSearchParams(updatedFilters).toString()}`);
  //   fetchBanners(1, pageSize, updatedFilters);
  // };

  const handleStatusChange = async (id: number, isActive: boolean) => {
    try {
      setLoading(true);

      // Show confirmation before deactivating
      if (!isActive) {
        Modal.confirm({
          title: "Deactivate banner",
          content: "Are you sure you want to deactivate this banner?",
          okText: "Yes",
          cancelText: "No",
          className: "custom-modal", // Apply custom class
          okButtonProps: { className: "custom-modal-ok-button" }, // Apply button class
          cancelButtonProps: { className: "custom-modal-cancel-button" },
          onOk: async () => {
            await updateBannerStatus(id, isActive);
          },
        });
      } else {
        // Allow API to handle validation for activating a banner
        await updateBannerStatus(id, isActive);
      }
    } catch (err) {
      message.error("Error updating banner status.");
    } finally {
      setLoading(false);
    }
  };

  const updateBannerStatus = async (id: number, isActive: boolean) => {
    try {
      await axiosInstance.put(`cms/menu/store-banner-details/${id}/`, {
        user_id: id,
        is_active: isActive,
      });

      message.success("Banner status updated.");
      setBanners((prev) =>
        prev.map((banner) =>
          banner.id === id ? { ...banner, is_active: isActive } : banner
        )
      );
    } catch (err: any) {
      // Backend will handle the validation and return an error if another banner is already active
      message.error(
        err.response?.data?.message || "Error updating banner status."
      );
    }
  };

  // const formatDate = (dateString: string): string => {
  //   return dayjs(dateString).format("DD/MM/YYYY, hh:mm a");
  // };

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: BannerObject) => (
        <Link
          className="common-link text-decoration-none"
          to={`/store/banner-details/${record.id}`}
        >
          {name}
        </Link>
      ),
    },
    {
      title: "Updated Date",
      dataIndex: "updated_at",
      key: "updated_at",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },

    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      render: (is_active: boolean, record: BannerObject) => (
        <div className="d-flex">
          <div
            className={`switch-button ${is_active ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !is_active)}
          >
            <span className="switch-label">
              {" "}
              {is_active ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  if (!loading && error) {
    <div className="error-container">
      <Alert message="Error" description={error} type="error" showIcon />
      <div className="retry-button d-flex justify-content-center align-items-center mt-3">
        <Button type="primary" onClick={() => fetchBanners()}>
          {Retry}
        </Button>
      </div>
    </div>;
  }

  return (
    <div className="mt-3">
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Store Banner List</div>
        <div className="d-flex">
          <Button
            type="primary"
            onClick={() => navigate(`/stores/${storeId}/add-banner`)}
          >
            Add Banner
          </Button>
          <Button type="primary" onClick={() => setImportModalVisible(true)}>
            Import Banner
          </Button>
        </div>
      </div>
      <div className="mt-3">
        <Table<BannerObject>
          bordered
          dataSource={banners}
          columns={columns}
          loading={loading}
          pagination={false}
          rowKey={(record) => record.id.toString()}
        />
        <div className="pagination">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
          />
        </div>
        <ImportBannerModal
          visible={isImportModalVisible}
          onClose={() => setImportModalVisible(false)}
          storeId={Number(storeId)}
          refreshData={fetchBanners}
        />
      </div>
    </div>
  );
};

export default StoreBanner;
