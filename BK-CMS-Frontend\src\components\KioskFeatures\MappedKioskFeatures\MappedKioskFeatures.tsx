import React, { useState, useEffect, useMemo, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button, Modal, message, Input, InputRef } from "antd";
import { axiosInstance } from "../../../apiCalls";
import DataTable from "../../shared/DataTable/commonDataTable";
import axios from "axios";
import {
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
} from "@ant-design/icons";

import dayjs from "dayjs";
import { useTableFilters } from "../../../hooks/useTableFilter";
import CommonPagination from "../../shared/Pagination/commonPagination";
import FilterButtons from "../../shared/FilterButton/FilterButton";
import { handleApiError } from "../../../utils/ApiErrorHandler";
import ErrorFallback from "../../Error/ErrorPage";

const API_ENDPOINT = "/cms/stores/features-kiosk";

export interface KioskFeature {
  id: number;
  feature_name: string;
  feature_code: string;
  kiosk_name: string;
  kiosk_code: string;
  is_active: boolean;
  created_at: string;
  kiosk: number;
  feature: number;
}

const MappedKioskFeatures: React.FC = ({}) => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  //  const location = useLocation();
  //const params = new URLSearchParams(location.search);

  // const initialPage = parseInt(params.get("page") || "1", 10);
  // const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  // const [currentPage, setCurrentPage] = useState<number>(initialPage);
  // const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [storeOptions, setStoreOptions] = useState<any[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const codeInputRef = useRef<InputRef>(null);

  const [searchValue, setSearchValue] = useState<string>("");

  const [error, setError] = useState<string | null>(null);

  const memoizedFilters = useMemo(() => filters, [filters]);

  //   const updateURLParams = useCallback(
  //     (updatedFilters: Record<string, string | number | undefined>) => {
  //       const params = new URLSearchParams();
  //       Object.entries(updatedFilters).forEach(([key, value]) => {
  //         if (value !== undefined) params.set(key, String(value));
  //       });
  //       navigate(`?${params.toString()}`, { replace: true });
  //     },
  //     [navigate]
  //   );

  // const fetchKiosksFeatures = useCallback(async () => {
  //   setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(`${API_ENDPOINT}/`, {
  //       params: {
  //         feature_id: id,
  //         page: currentPage,
  //         page_size: pageSize,
  //         ...memoizedFilters,
  //       },
  //     });

  //     if (response.status === 200) {
  //       setStoreOptions(response.data.objects);
  //       setTotalCount(response.data.total_count);
  //     } else {
  //       setStoreOptions([]);
  //       // setTotalCount(0);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching stores:", error);
  //     notification.error({
  //       message: "Error",
  //       description: "Failed to load stores.",
  //     });
  //   } finally {
  //     setLoading(false);
  //   }
  // }, [currentPage, pageSize, id]);

  // useEffect(() => {
  //   fetchKiosksFeatures();
  // }, [currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchKiosksFeatures = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get(`${API_ENDPOINT}/`, {
          params: {
            feature_id: id,
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
          signal: controller.signal,
        });

        if (response.status === 200) {
          setStoreOptions(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchKiosksFeatures();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  // useEffect(() => {
  //   const params = new URLSearchParams(location.search);

  //   setCurrentPage(parseInt(params.get("page") || "1", 10));
  //   setPageSize(parseInt(params.get("page_size") || "10", 10));
  // }, [location.search]);

  //   const handlePageChange = (page: number, pageSize?: number) => {
  //     setCurrentPage(page);
  //     setPageSize(pageSize || 10);
  //     updateURLParams({ page, page_size: pageSize || 10 });
  //   };

  const handleStatusChange = (id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Status" : "Deactivate Status",
      content: isActive
        ? "Are you sure you want to activate this Status?"
        : "Are you sure you want to deactivate this Status?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal", // Apply custom class
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          setLoading(true);
          const response = await axiosInstance.patch(
            `/cms/stores/features-kiosk-update/${id}/`,
            {
              is_active: isActive,
            }
          );

          if (response.status === 200) {
            message.success(`Status updated successfully.`);
            setStoreOptions((prevData: any) =>
              prevData.map((feature: any) =>
                feature.id === id
                  ? { ...feature, is_active: isActive }
                  : feature
              )
            );
          } else {
            message.error("Failed to update user status.");
          }
        } catch (err: unknown) {
          if (axios.isAxiosError(err)) {
            message.error(
              err.response?.data?.message ||
                "An error occurred while updating user status."
            );
          } else {
            message.error("An unexpected error occurred.");
          }
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  const clearAllFiltersHandler = () => {
    clearAllFilters();
    setSearchValue("");
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    );
  };

  const handleFocus = () => {
    if (!isFocused) {
      setIsFocused(true);
      codeInputRef.current?.focus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const columns = [
    {
      title: "Kiosk Code",
      dataIndex: "kiosk_code",
      key: "kiosk_code",
      width: "10%",
      //   render: (text: string, record: any) =>
      //     text ? (
      //       <Link
      //         className="common-link text-decoration-none"
      //         to={`/stores/${record.store_id}/details`}
      //       >
      //         {text}
      //       </Link>
      //     ) : (
      //       "N/A"
      //     ),
    },
    {
      title: "Kiosk Name",
      width: "20%",
      dataIndex: "kiosk_name",
      key: "kiosk_name",
    },
    {
      title: "Feature Code",
      dataIndex: "feature_code",
      key: "feature_code",
      width: "15%",
    },
    {
      title: "Feature Name",
      dataIndex: "feature_name",
      key: "feature_name",
      width: "20%",
    },
    {
      title: "Date",
      dataIndex: "created_at",
      key: "created_at",
      width: "17%",
      // sorter: (a: Order, b: Order) =>
      //   dayjs(a.created_at).unix() - dayjs(b.created_at).unix(),
      render: (createdAt: string) =>
        dayjs(createdAt).format("DD MMM YYYY, hh:mm: A"), // 12-hour format  //for 24-hour format: dayjs(createdAt).format("DD MMM 'YYYY, HH:mm:ss")
    },

    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      width: "10%",
      fixed: "right" as "right",
      render: (is_active: boolean, record: KioskFeature) => (
        <div className="d-flex">
          <div
            className={`switch-button ${is_active ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !is_active)}
          >
            <span className="switch-label">
              {" "}
              {is_active ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div>
      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver mr-2">
            <Input.Search
              ref={codeInputRef}
              placeholder="Search by code & name"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={(value) => {
                if (value.trim()) {
                  handleSearchChange(value);
                }
              }}
              onFocus={handleFocus}
              onBlur={handleBlur}
              prefix={
                <SearchOutlined className={isFocused ? "" : "invisible"} />
              }
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearAllFilters={clearAllFiltersHandler}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
        </div>
        <div>
          <Button
            type="primary"
            className="btn-save"
            onClick={() => navigate(`/add-features-to-kiosk/${id}`)}
          >
            Add Feature to Kisoks
          </Button>
        </div>
      </div>

      <div className="mt-4">
        <DataTable<KioskFeature>
          columns={columns}
          dataSource={storeOptions}
          loading={loading}
          pagination={false}
          rowKey="id"
        />

        <div className="pagination">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </div>
    </div>
  );
};

export default MappedKioskFeatures;
