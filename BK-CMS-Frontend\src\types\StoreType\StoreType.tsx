// Define the type for formData to allow string, number, boolean, or null
export interface StoreFormData {
  key: string;
  name: string;
  code: string;
  ato_id: string;
  // edc_store_id?: string;
  edc_merchant_id: string;
  timezone: string | number;
  address: string;
  phone: string;
  postal_code: string;
  latitude: string | number | null;
  longitude: string | number | null;
  tax_percentage: string | number;
  can_accept_delivery_order: boolean;
  is_active: boolean;
  coverage_type: string;
  third_party_id: string;
  take_away_charge: string | number;
  is_cash_payment_available: boolean;
  is_card_payment_available: boolean;
  is_qr_code_payment_available: boolean;
  [key: string]: string | boolean | number | null | undefined;
}
