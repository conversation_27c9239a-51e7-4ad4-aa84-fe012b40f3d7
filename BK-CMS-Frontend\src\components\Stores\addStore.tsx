import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Form,
  Input,
  Button,
  Select,
  Checkbox,
  InputNumber,
  message,
  notification,
} from "antd";
import { axiosInstance } from "../../apiCalls";
import { StoreFormData } from "../../types/StoreType/StoreType";
import { timezones } from "../../utils/TimeZoneList"; // List of timezones
import { Cancel, Save, Saving } from "../../constants/Constant";

const { Option } = Select;

const AddStore: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);

  const onFinish = async (values: StoreFormData) => {
    setLoading(true);
    try {
      const formattedValues = {
        ...values,
        timezone:
          typeof values.timezone === "number"
            ? values.timezone.toFixed(1)
            : values.timezone,
      };

      const response = await axiosInstance.post(
        "/cms/stores/stores/",
        formattedValues
      );
      if (response.status === 201) {
        message.success("Store Created Successfully!");
        navigate("/stores-list");
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred.";

      if (error.response) {
        const errorData = error.response.data;

        if (typeof errorData === "object" && !Array.isArray(errorData)) {
          errorMessage = Object.entries(errorData)
            .map(([field, messages]) => {
              if (
                Array.isArray(messages) &&
                messages.every((msg) => typeof msg === "string")
              ) {
                return `${field}: ${messages.join(", ")}`;
              }
              return `${field}: ${String(messages)}`;
            })
            .join("\n"); // Formatting error messages
        } else {
          errorMessage = errorData?.message || JSON.stringify(errorData);
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your network.";
      } else {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Add Store</div>
      </div>
      <div className="pt-4 mt-4">
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 14 }}
          className="mt-2"
          style={{ maxWidth: "600px" }}
          autoComplete="off"
          onFinish={onFinish}
          initialValues={{
            coverage_type: "Radius",
            can_accept_delivery_order: false,
            is_active: false,
            is_cash_payment_available: false,
            is_card_payment_available: false,
            is_qr_code_payment_available: false,
          }}
        >
          {/* Store Name and Code */}
          <Form.Item
            name="name"
            label="Store Name"
            rules={[{ required: true, message: "Please enter store name" }]}
          >
            <Input placeholder="Enter store name" />
          </Form.Item>

          <Form.Item
            name="code"
            label="Store Code"
            rules={[{ required: true, message: "Please enter store code" }]}
          >
            <Input placeholder="Enter store code" />
          </Form.Item>

          <Form.Item
            name="ato_id"
            label="ATO ID"
            rules={[{ required: true, message: "Please enter ATO ID" }]}
          >
            <Input placeholder="Enter ATO ID" />
          </Form.Item>

          {/* <Form.Item
            name="edc_store_id"
            label="EDC Store ID"
            rules={[{ required: true, message: "Please enter EDC Store ID" }]}
          >
            <Input type="number" placeholder="Enter EDC Store ID" />
          </Form.Item> */}

          <Form.Item
            name="edc_merchant_id"
            label="EDC Merchant ID"
            rules={[
              { required: true, message: "Please enter EDC Merchant ID" },
            ]}
          >
            <Input type="number" placeholder="Enter EDC Merchant ID" />
          </Form.Item>

          {/* Timezone */}
          <Form.Item
            name="timezone"
            label="Timezone"
            rules={[{ required: true, message: "Please select a timezone" }]}
          >
            <Select
              placeholder="Select Timezone"
              getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
            >
              {timezones.map((tz) => (
                <Option key={tz.value} value={tz.value}>
                  {tz.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Phone and Postal Code */}
          <Form.Item
            name="phone"
            label="Phone"
            rules={[
              { required: true, message: "Please enter a phone number" },
              {
                pattern: /^[0-9]{10}$/, // Regex to allow exactly 10 digits
                message: "Phone number must be exactly 10 digits",
              },
            ]}
          >
            <Input placeholder="Enter phone number" maxLength={10} type="tel" />
          </Form.Item>

          <Form.Item
            name="postal_code"
            label="Postal Code"
            rules={[
              { required: true, message: "Please enter postal code" },
              {
                pattern: /^[0-9]{6}$/,
                message: "Postal code must be exactly 6 digits",
              },
            ]}
          >
            <Input maxLength={6} placeholder="Enter postal code" />
          </Form.Item>

          {/* Address */}
          <Form.Item
            name="address"
            label="Address"
            rules={[{ required: true, message: "Please enter an address" }]}
          >
            <Input.TextArea rows={3} placeholder="Enter address" />
          </Form.Item>

          {/* Latitude and Longitude */}
          <Form.Item
            name="latitude"
            label="Latitude"
            rules={[{ required: true, message: "Please enter latitude" }]}
          >
            <InputNumber placeholder="Enter latitude" className="w-100" />
          </Form.Item>

          <Form.Item
            name="longitude"
            label="Longitude"
            rules={[{ required: true, message: "Please enter longitude" }]}
          >
            <InputNumber placeholder="Enter longitude" className="w-100" />
          </Form.Item>

          {/* Take Away Charge and Tax Percentage */}
          <Form.Item
            name="take_away_charge"
            label="Take Away Charge"
            rules={[
              { required: true, message: "Please enter take away charge" },
            ]}
          >
            <InputNumber
              className="w-100"
              placeholder="Enter take away charge"
            />
          </Form.Item>

          <Form.Item
            name="tax_percentage"
            label="Tax Percentage"
            rules={[{ required: true, message: "Please enter tax percentage" }]}
          >
            <InputNumber
              className="w-100"
              placeholder="Enter tax percentage"
              min={0}
              max={100}
            />
          </Form.Item>

          {/* Third Party ID */}
          <Form.Item
            name="third_party_id"
            label="Third Party ID"
            rules={[
              { required: true, message: "Please enter a third party ID" },
            ]}
          >
            <Input placeholder="Enter third party ID" />
          </Form.Item>

          {/* Coverage Type */}
          <Form.Item
            name="coverage_type"
            label="Coverage Type"
            rules={[
              { required: true, message: "Please select a coverage type" },
            ]}
          >
            <Select>
              <Option value="Radius">Radius</Option>
              <Option value="Polygon">Polygon</Option>
            </Select>
          </Form.Item>

          {/* Store Config Data */}
          {/* Tax Config */}
          <Form.Item label="Tax Config" className="mb-0">
            <Form.Item
              name={["store_cofig_data", "tax_config", "cgst"]}
              label="CGST"
              rules={[
                { required: true, message: "Please enter CGST percentage" },
              ]}
            >
              <InputNumber
                placeholder="Enter CGST"
                min={0}
                max={100}
                className="w-100"
              />
            </Form.Item>
            <Form.Item
              name={["store_cofig_data", "tax_config", "sgst"]}
              label="SGST"
              rules={[
                { required: true, message: "Please enter SGST percentage" },
              ]}
            >
              <InputNumber
                placeholder="Enter SGST"
                min={0}
                max={100}
                className="w-100"
              />
            </Form.Item>
          </Form.Item>

          {/* Payment Config */}
          <Form.Item label="Payment Config">
            <Form.Item
              name={["store_cofig_data", "payment_config", "qr"]}
              valuePropName="checked"
            >
              <Checkbox>QR Payment Available</Checkbox>
            </Form.Item>
            <Form.Item
              name={["store_cofig_data", "payment_config", "card"]}
              valuePropName="checked"
            >
              <Checkbox>Card Payment Available</Checkbox>
            </Form.Item>
            <Form.Item
              name={["store_cofig_data", "payment_config", "cash"]}
              valuePropName="checked"
            >
              <Checkbox>Cash Payment Available</Checkbox>
            </Form.Item>
            <Form.Item
              name={["store_cofig_data", "payment_config", "edc_qr"]}
              valuePropName="checked"
            >
              <Checkbox>EDC QR Payment Available</Checkbox>
            </Form.Item>
          </Form.Item>

          {/* Checkbox Option for Active */}
          <Form.Item label={null}>
            <Form.Item name="is_active" valuePropName="checked" noStyle>
              <Checkbox>Is Active</Checkbox>
            </Form.Item>
          </Form.Item>

          {/* Submit and Cancel Buttons */}
          <Form.Item label={null}>
            <Button
              type="primary"
              className="btn-save me-4"
              htmlType="submit"
              loading={loading}
            >
              {loading ? `${Saving}` : `${Save}`}
            </Button>
            <Button
              className="btn-cancel"
              onClick={() => navigate(`/stores-list`)}
            >
              {Cancel}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default AddStore;
