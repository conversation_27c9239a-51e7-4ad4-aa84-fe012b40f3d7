import { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, notification, AutoComplete } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { Cancel } from "../../../constants/Constant";

const ImportBannerModal = ({
  visible,
  onClose,
  storeId,
  refreshData,
}: {
  visible: boolean;
  onClose: () => void;
  storeId: number;
  refreshData: () => void;
}) => {
  const [bannerOptions, setBannerOptions] = useState<{ id: number; name: string }[]>([]);
  const [searchValue, setSearchValue] = useState<string>("");

  // Debounced API call for searching banners
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchValue.trim()) {
        axiosInstance
          .get("cms/menu/banners/", {
            params: { name: searchValue, page: 1, page_size: 10 },
          })
          .then((response) => {
            const banners =
              response.data.objects?.map((banner: any) => ({
                id: banner.id,
                name: banner.name,
              })) || [];
            setBannerOptions(banners);
          })
          .catch((error) => {
            console.error("Error fetching banners:", error);
          });
      } else {
        setBannerOptions([]);
      }
    }, 500); // 500ms debounce time

    return () => clearTimeout(delayDebounceFn);
  }, [searchValue]);

  const handleMap = () => {
    const selectedBanner = bannerOptions.find((banner) => banner.name === searchValue);

    if (!selectedBanner) {
      notification.error({
        message: "Invalid Selection",
        description: "Please select a valid banner.",
      });
      return;
    }

    const payload = {
      banner_id: selectedBanner.id,
      store_ids: [storeId],
    };

    axiosInstance
      .post("/cms/menu/store-banners/", payload)
      .then(() => {
        notification.success({
          message: "Mapping Successful",
          description: "The banner has been mapped to the store.",
        });
        onClose();
        refreshData();
        setSearchValue(""); // Reset input after mapping
      })
      .catch((error) => {
        console.error("Error mapping banner:", error.response?.data || error);
        notification.error({
          message: "Mapping Failed",
          description: "An error occurred while mapping the banner to the store.",
        });
      });
  };

  return (
    <Modal title="Import and Map Banner" open={visible} onCancel={onClose} footer={null}>
      <div>
        <div style={{ marginBottom: "1rem" }}>
          <label>Search Banner</label>
          <AutoComplete
            placeholder="Search by banner name"
            options={bannerOptions.map((banner) => ({
              value: banner.name,
              label: banner.name,
            }))}
            value={searchValue} // Ensuring input shows selected value
            onSearch={setSearchValue} // Updates search input
            onSelect={(value) => setSearchValue(value)} // Updates input when selecting an option
            style={{ width: "100%" }}
          />
        </div>
        <Button className="btn-save" type="primary" onClick={handleMap} style={{ marginTop: "1rem" }}>
          Map Banner to Store
        </Button>
        <Button className="btn-cancel" onClick={onClose} style={{ marginTop: "1rem" }}>{Cancel}</Button>
      </div>
    </Modal>
  );
};

export default ImportBannerModal;
