import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { Table, Button, InputRef, Pagination, Input, Alert } from "antd";

import { axiosInstance } from "../../apiCalls";
import "../../assets/css/Kiosk/KioskList.css"; // Import the CSS file
import {
  Object as BannerObject,
  ResponseData,
} from "../../types/BannerType/BannerType";
import { Link, useLocation, useNavigate } from "react-router-dom";

import dayjs from "dayjs";
import { handleApiError } from "../../utils/ApiErrorHandler";
import {
  Add_New_Banner,
  Banners,
  Clear_All,
  Retry,
} from "../../constants/Constant";
import { CloseOutlined } from "@ant-design/icons";

const MasterBannerList: React.FC = () => {
  const [data, setData] = useState<BannerObject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const [totalCount, setTotalCount] = useState<number>(0);

  const location = useLocation();
  const navigate = useNavigate();

  const codeInputRef = useRef<InputRef>(null);

  const params = new URLSearchParams(location.search);
  const initialPage = parseInt(params.get("page") || "1", 10);
  const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [filters, setFilters] = useState<Record<string, string | undefined>>(
    () => {
      const filterObj: Record<string, string | undefined> = {};
      params.forEach((value, key) => {
        if (!["page", "page_size"].includes(key)) {
          filterObj[key] = value;
        }
      });
      return filterObj;
    }
  );
  const appliedFilters = Object.keys(filters).filter(
    (key) => key !== "page" && key !== "page_size"
  );
  const showClearButtons = appliedFilters.length > 0;

  const memoizedFilters = useMemo(() => filters, [filters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchBanners = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<ResponseData>(
          "/cms/menu/banners/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setData(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: unknown) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);

    const updatedFilters = {
      ...filters,
      page: page.toString(),
      page_size: size?.toString() || pageSize.toString(),
    };

    setFilters(updatedFilters); // Update filters in state
    updateURLParams(updatedFilters); // Update URL
  };

  const handleFilterChange = async (key: string, value: string) => {
    const updatedFilters: Record<string, string | undefined> = { ...filters };

    if (value === "All") {
      delete updatedFilters[key]; // Remove only the specific filter
    } else {
      updatedFilters[key] = value;
    }

    updatedFilters["page"] = "1"; // Always reset to page 1 when changing filters

    setFilters(updatedFilters);
    updateURLParams(updatedFilters);
  };

  // Restore filters when user navigates back using browser
  const handlePopState = useCallback(() => {
    const updatedParams = new URLSearchParams(window.location.search);
    const newFilters: Record<string, string | undefined> = {};

    updatedParams.forEach((value, key) => {
      if (!["page", "page_size"].includes(key)) {
        newFilters[key] = value;
      }
    });

    const newPage = parseInt(updatedParams.get("page") || "1", 10) || 1;
    const newPageSize =
      parseInt(updatedParams.get("page_size") || "10", 10) || 10;

    setFilters(newFilters);
    setCurrentPage(newPage);
    setPageSize(newPageSize);
  }, []);

  useEffect(() => {
    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [handlePopState]);

  // Update URL params when filters change
  const updateURLParams = (
    updatedFilters: Record<string, string | undefined>
  ) => {
    const params = new URLSearchParams();

    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value); //  Add only active filters to URL
      }
    });

    if (params.toString()) {
      navigate(`?${params.toString()}`); //  Maintain history by adding a new entry
    } else {
      navigate(location.pathname); //  Clear filters from URL but keep history
    }
  };
  // Remove a single filter and update the URL
  const clearFilter = (key: string) => {
    const updatedFilters = { ...filters };
    delete updatedFilters[key];

    setFilters(updatedFilters);

    // Update URL
    const params = new URLSearchParams();
    Object.entries(updatedFilters).forEach(([k, v]) => {
      if (v) params.set(k, v);
    });

    navigate(params.toString() ? `?${params.toString()}` : location.pathname);
  };

  // Clear all filters and reset URL
  const clearAllFilters = () => {
    setFilters({});

    navigate(location.pathname); // Reset URL
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const columns = [
    {
      title: "Banner Name",
      dataIndex: "name",
      key: "name",
      width: "15%",
      fixed: "left" as "left",
      filteredValue: filters.name ? ([filters.name] as string[]) : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Search Payment ID"
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => {
              confirm();
              handleFilterChange("name", selectedKeys[0]); // Update URL
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("name", "All"); // Reset URL filter
                  }}
                  className="search-filter-clear-btn"
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
          }
        },
      },
      render: (code: string, record: BannerObject) => (
        <Link
          className="common-link text-decoration-none"
          to={`/banner-details/${record.id}`}
        >
          {code}
        </Link>
      ),
    },
    {
      title: "Date",
      dataIndex: "updated_at",
      key: "updated_at",
      width: "15%",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },
    // {
    //   title: "Status",
    //   dataIndex: "is_active",
    //   key: "is_active",
    //   width: "10%",
    //   render: (is_active: boolean) => (
    //     <Text
    //       style={{
    //         color: is_active ? "green" : "red",
    //         fontWeight: "bold",
    //       }}
    //     >
    //       {is_active ? "Active" : "Inactive"}
    //     </Text>
    //   ),
    // },
  ];

  if (!loading && error) {
    return (
      <div className="error-container">
        <Alert message="Error" description={error} type="error" showIcon />
        <div className="retry-button d-flex justify-content-center align-items-center mt-3">
          <Button type="primary" onClick={() => window.location.reload()}>
            {Retry}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>{Banners}</div>
        <Button
          type="primary"
          className="add-store"
          onClick={() => navigate("/add-new-banner")}
        >
          {Add_New_Banner}
        </Button>
      </div>
      <div className="pt-4">
        {showClearButtons && (
          <Button
            type="default"
            className="filter-btn"
            onClick={clearAllFilters}
          >
            {Clear_All}
          </Button>
        )}

        {appliedFilters.map((key) => (
          <Button
            key={key}
            type="default"
            onClick={() => clearFilter(key)}
            className="filter-btn"
          >
            {formatPaymentMethod(filters[key] || "")}<CloseOutlined />
          </Button>
        ))}
      </div>
      <div className="pt-2 mt-2 mb-4">
        <Table
          dataSource={data}
          columns={columns}
          loading={loading}
          rowKey={"id"}
          pagination={false}
        />
        <div className="pagination">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            showSizeChanger
            onShowSizeChange={handlePageChange}
            onChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  );
};

export default MasterBannerList;
