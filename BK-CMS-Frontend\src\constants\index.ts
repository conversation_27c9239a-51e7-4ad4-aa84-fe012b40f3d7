
export const topBarContent = [
  {
    id: 1,
    label: "Users",
    link: "/users-list",
  },
  {
    id: 2,
    label: "Stores",
    link: "/stores-list",
  },
  {
    id: 3,
    label: "Menus",
    link: "/menus-list",
  },
  {
    id: 4,
    label: "Kiosks",
    link: "/kiosks",
  },
  {
    id: 5,
    label: "Orders",
    link: "/orders-list",
  },
];

// user table headers
export const userTableHeaders = {
  id: "Id",
  username: "<PERSON>rna<PERSON>",
  first_name: "First_name",
  last_name: "Last_name",
  email: "Email",
  is_active: "Is_active",
  is_staff: "Is_staff",
  date_joined: "Date_joined",
  is_superuser: "Is_superuser",
  user_type: "User_type",
  code: "Code",
  last_login: "Last_login",
  business: "Business",
  groups: "Groups",
  user_permissions: "User_permissions",
};

// order table headers
export const orderTableHeaders = {
  id: "ID",
  tableNumber: "Table Number",
  orderType: "Order Type",
  orderStatus: "Order Status",
  notes: "Notes",
  paymentMethod: "Payment Method",
  paymentStatus: "Payment Status",
  paymentId: "Payment ID",
  donationEnabled: "Donation Enabled",
  grandTotal: "Grand Total",
  taxableValue: "Taxable Value",
  discountValue: "Discount Value",
  rounding: "Rounding",
  posOrderNumber: "POS Order Number",
  queueNumber: "Queue Number",
  kioskTerminalId: "Kiosk Terminal ID",
  createdAt: "Created At",
  customer: "Customer",
  store: "Store",
};

//side nav bar content

export const sideNavBarContent = [
  {
    id: "1",
    label: "Users",
   
  },
  {
    id: "2",
    label: "Stores",

    subData: ["Stores", "Kios", "Config"], // Corrected formatting
  },
  {
    id: "3",
    label: "Kios",
  },
  {
    id: "4",
    label: "Orders",
  },
  {
    id: "5",
    label: "Menus",
  },
];
