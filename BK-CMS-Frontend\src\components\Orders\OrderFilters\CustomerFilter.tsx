import { Input, Spin } from "antd";
import { useEffect, useRef, useState } from "react";
import axios from "axios";
import { axiosInstance } from "../../../apiCalls";

const CustomerFilterDropdown = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  handleFilterChange,
  inputRef,
}: any) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [query, setQuery] = useState<string>("");

  const controllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    if (inputRef?.current) {
      inputRef.current.focus({ cursor: "end" });
    }
  }, [selectedKeys]);

  // API Call (Only triggers on Enter key press)
  const fetchCustomers = async (searchQuery: string) => {
    if (!searchQuery.trim()) return; // No API call if the input is empty

    if (controllerRef.current) {
      controllerRef.current.abort(); // Cancel previous request
    }

    const controller = new AbortController();
    controllerRef.current = controller;

    setLoading(true);
    try {
      const response = await axiosInstance.get("/pos/customers/", {
        params: {
          name: searchQuery,
          page: "1",
          page_size: "10",
        },
        signal: controller.signal,
      });

      if (response.status !== 200) {
        console.warn("Unexpected API response:", response);
      }
    } catch (error) {
      if (!axios.isCancel(error)) {
        console.error("Error fetching customer data:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="filter-dropdown" style={{ padding: 8 }}>
      <Input
        ref={inputRef}
        placeholder="Enter customer Name"
        value={selectedKeys[0] || query}
        onChange={(e) => {
          setQuery(e.target.value);
          setSelectedKeys(e.target.value ? [e.target.value] : []);
        }}
        onFocus={() => {
          if (selectedKeys[0]) {
            setQuery(selectedKeys[0]);
          }
        }}
        onPressEnter={() => {
          confirm();
          handleFilterChange("customer", query || "");
          fetchCustomers(query); // Trigger API call when Enter is pressed
        }}
        suffix={
          <span
            onClick={() => {
              setQuery("");
              setSelectedKeys([]);
              clearFilters();
              confirm();
              handleFilterChange("customer", "All");
            }}
            style={{
              cursor: "pointer",
              color: "#999",
              opacity: query ? 1 : 0,
              pointerEvents: query ? "auto" : "none",
              transition: "opacity 0.2s ease-in-out",
            }}
          >
            ✖
          </span>
        }
      />
      {loading && <Spin size="small" style={{ marginLeft: 8 }} />}
    </div>
  );
};

export default CustomerFilterDropdown;
