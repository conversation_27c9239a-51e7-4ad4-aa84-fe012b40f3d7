import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  notification,
  Upload,
  Select,
  Button,
  Progress,
  Spin,
  Card,
  message,
} from "antd";
import { LoadingOutlined, UploadOutlined } from "@ant-design/icons";
import { RcFile } from "antd/es/upload";
import { useNavigate } from "react-router-dom";
import { useUploadFile } from "../../../../hooks/useCreativeUpload";
import { axiosInstance } from "../../../../apiCalls";
import { handleApiError } from "../../../../utils/ApiErrorHandler";
import ErrorFallback from "../../../Error/ErrorPage";

const { Option } = Select;

const AddCreativeFile: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [fileType, setFileType] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<RcFile | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isloading, setIsloading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [dynamicAttributes, setDynamicAttributes] = useState({
    creative_types: [],
  });

  const {
    uploading,
    uploadProgress,
    uploadSuccess,
    fileKey,
    contentType,
    getPresignedUrlAndUpload,
    resetUploadState,
  } = useUploadFile();

  const allFilesUploaded: boolean | undefined =
    !!selectedFile && uploadSuccess !== null ? uploadSuccess : undefined;

  const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
  const MAX_VIDEO_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif"];
  const ALLOWED_VIDEO_TYPES = [
    "video/mp4",
    "video/mov",
    "video/avi",
    "video/mkv",
  ];

  const beforeUpload = (file: RcFile) => {
    const isImage = ALLOWED_IMAGE_TYPES.includes(file.type);
    const isVideo = ALLOWED_VIDEO_TYPES.includes(file.type);
    const isValidType = isImage || isVideo;

    if (!isValidType) {
      notification.error({
        message: "Invalid File Type",
        description:
          "Only images (jpg, png, gif) and videos (mp4, mov, avi, mkv) are allowed.",
      });
      return Upload.LIST_IGNORE;
    }

    const isValidSize = isImage
      ? file.size <= MAX_IMAGE_SIZE
      : file.size <= MAX_VIDEO_SIZE;

    if (!isValidSize) {
      notification.error({
        message: "File Too Large",
        description: `Maximum size is ${
          isImage ? "5MB for image" : "10MB for video"
        }.`,
      });
      return Upload.LIST_IGNORE;
    }

    setSelectedFile(file);
    return false;
  };

  useEffect(() => {
    const controller = new AbortController();

    const fetchAttributes = async () => {
      try {
        setIsloading(true);
        const response = await axiosInstance.get("/pos/orders/meta-data/", {
          signal: controller.signal,
        });

        if (response.status === 200) {
          setDynamicAttributes(response.data.creative_meta);
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setIsloading(false);
      }
    };

    fetchAttributes();

    return () => {
      controller.abort();
    };
  }, []);

  //Reset upload progress on file type change
  const handleFileTypeChange = (value: string) => {
    setFileType(value);
    form.resetFields(["textvalue"]);
    setSelectedFile(null);
    resetUploadState();
  };

  const handleSubmit = async (values: any) => {
    try {
      const isTextType =
        fileType === "welcome_text" || fileType === "background_color";

      setLoading(true);
      const response = await axiosInstance.post("/cms/menu/creatives_files/", {
        name: values.name,
        type: fileType,
        source: isTextType ? values.textvalue : fileKey,
        content_type: isTextType
          ? "text"
          : contentType.startsWith("image")
          ? "image"
          : "video",
      });

      if (response.status === 201 && response.data.id) {
        message.success(response.data.message);
        navigate(`/creative-file-details/${response.data.id}`);
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred.";

      if (error.response) {
        const errorData = error.response.data;

        if (typeof errorData === "object" && !Array.isArray(errorData)) {
          errorMessage = Object.entries(errorData)
            .map(([field, messages]) => {
              if (
                Array.isArray(messages) &&
                messages.every((msg) => typeof msg === "string")
              ) {
                return `${field}: ${messages.join(", ")}`;
              }
              return `${field}: ${String(messages)}`;
            })
            .join("\n");
        } else {
          errorMessage = errorData?.message || JSON.stringify(errorData);
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your network.";
      } else {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const formatText = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  if (!loading && error) {
    return (
      <ErrorFallback error={error} onRetry={() => window.location.reload()} />
    );
  }

  if (isloading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <div className="heading-title">Add Creative File</div>
      <Card className="mt-3">
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
          onFinish={handleSubmit}
        >
          <Form.Item label="Name" name="name" rules={[{ required: true }]}>
            <Input placeholder="Enter name" />
          </Form.Item>
          <Form.Item
            label="Select File Type"
            name="fileType"
            rules={[{ required: true, message: "Please select a file type!" }]}
          >
            <Select
              onChange={handleFileTypeChange}
              placeholder="Select file type"
            >
              {dynamicAttributes.creative_types.map((type) => (
                <Option key={type} value={type}>
                  {formatText(type)}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Show text input if file type is welcome_text or background_color */}
          {fileType === "welcome_text" || fileType === "background_color" ? (
            <Form.Item
              label="Enter Text"
              name="textvalue"
              rules={[{ required: true, message: "Please enter text!" }]}
            >
              <Input placeholder="Enter text here" />
            </Form.Item>
          ) : null}

          {/* Show file upload only if a valid fileType is selected */}
          {fileType &&
            fileType !== "welcome_text" &&
            fileType !== "background_color" && (
              <>
                <Form.Item label="Select File">
                  <Upload
                    beforeUpload={beforeUpload}
                    fileList={selectedFile ? [selectedFile] : []}
                    onRemove={() => {
                      setSelectedFile(null);
                      resetUploadState();
                    }}
                    disabled={allFilesUploaded}
                  >
                    <Button
                      icon={<UploadOutlined />}
                      disabled={uploading || allFilesUploaded}
                    >
                      Select File
                    </Button>
                  </Upload>
                </Form.Item>
                {selectedFile && (
                  <Form.Item
                    label={
                      uploading
                        ? "Uploading..."
                        : allFilesUploaded
                        ? "Uploaded"
                        : "Upload"
                    }
                  >
                    <Button
                      onClick={() =>
                        getPresignedUrlAndUpload(selectedFile, fileType)
                      }
                      disabled={uploading || allFilesUploaded}
                    >
                      {uploading
                        ? "Uploading..."
                        : allFilesUploaded
                        ? "Uploaded"
                        : "Upload"}
                    </Button>
                    {uploadProgress > 0 && (
                      <Progress percent={uploadProgress} />
                    )}
                  </Form.Item>
                )}
              </>
            )}

          <Form.Item label={null}>
            <Button
              type="primary"
              className="btn-save"
              htmlType="submit"
              disabled={!fileType}
            >
              {loading ? <LoadingOutlined spin /> : "Save"}
            </Button>
            <Button
              type="default"
              className="btn-cancel"
              onClick={() => navigate(-1)}
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default AddCreativeFile;
