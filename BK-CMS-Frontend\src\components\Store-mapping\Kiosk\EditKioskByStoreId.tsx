import { Form, Input, Switch, Button, message, Select } from "antd";
import { useEffect, useState, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import {
  kioskTypes,
  STORE_TYPE_CHOICES,
} from "./SelectTypeKiosk/SelectTypeKioksList";
import { KioskType } from "../../../types/KioskType/KioskType";
import {
  Btn_Update_Kiosk,
  Cancel,
  Edit_Kiosk,
  Saving,
} from "../../../constants/Constant";

const { Option } = Select;

const EditKiosk: React.FC = () => {
  const { storeId, kioskId } = useParams<{
    storeId: string;
    kioskId: string;
  }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedKiosk, setSelectedKiosk] = useState<string | undefined>(
    undefined
  );

  useEffect(() => {
    const fetchKioskDetails = async (): Promise<void> => {
      try {
        setLoading(true);
        const response = await axiosInstance.get(
          `/cms/stores/${storeId}/kiosks/`
        );
        const kioskData = response.data.objects.find(
          (kiosk: KioskType) => kiosk.id === parseInt(kioskId!, 10)
        );

        if (kioskData) {
          form.setFieldsValue({
            code: kioskData.code,
            name: kioskData.name,
            version: kioskData.version,
            edc_store_id: kioskData.edc_store_id,
            edc_clientiD: kioskData.edc_clientiD,
            edc_securitytoken: kioskData.edc_securitytoken,
            teamviewer_id: kioskData.teamviewer_id,
            anydesk_id: kioskData.anydesk_id,
            printer_serial_number: kioskData.printer_serial_number,
            store: kioskData.store,
            type: kioskData.type,
            store_location_type: kioskData.store_location_type,
            is_active: kioskData.is_active,
          });
          // setSelectedStoreType(kioskData.store_location_type);
          setSelectedKiosk(kioskData.type);
        } else {
          message.error("Kiosk not found.");
        }
      } catch (error: any) {
        message.error(error.message);
      } finally {
        setLoading(false);
      }
    };
    fetchKioskDetails();
  }, [storeId, kioskId, form]);

  // Handle the form submission to update the kiosk data
  const handleEditKiosk = useCallback(
    async (values: KioskType) => {
      try {
        setLoading(true);
        const payload = {
          ...values,
          store: parseInt(storeId!, 10),
          kiosk_id: parseInt(kioskId!, 10),
          // store_location_type: selectedStoreType,
          type: selectedKiosk,
        };

        const response = await axiosInstance.put(
          `/cms/stores/${storeId}/kiosks/${kioskId}/update/`,
          payload
        );

        if (response.status === 200) {
          message.success("Kiosk updated successfully");
          navigate(`/stores/${storeId}/kiosk`);
        } else {
          message.error("Failed to update kiosk.");
        }
      } catch (error: any) {
        let errorMessage = "An unexpected error occurred.";

        if (error.response) {
          const errorData = error.response.data;

          if (typeof errorData === "object" && !Array.isArray(errorData)) {
            errorMessage = Object.entries(errorData)
              .map(([field, messages]) => {
                if (
                  Array.isArray(messages) &&
                  messages.every((msg) => typeof msg === "string")
                ) {
                  return `${field}: ${messages.join(", ")}`;
                }
                return `${field}: ${String(messages)}`;
              })
              .join("\n"); // Formatting error messages
          } else {
            errorMessage = errorData?.message || JSON.stringify(errorData);
          }
        } else if (error.request) {
          errorMessage = "No response from server. Please check your network.";
        } else {
          errorMessage = error.message;
        }
        message.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [storeId, kioskId, navigate, selectedKiosk]
  );

  return (
    <div>
      <div className="heading-title">{Edit_Kiosk}</div>
      <div className="pt-3" />
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600, marginTop: 20 }}
        autoComplete="off"
        onFinish={handleEditKiosk}
      >
        {/* Code Field */}
        <Form.Item
          name="code"
          label="Code"
          rules={[{ required: true, message: "Please input the kiosk code!" }]}
        >
          <Input placeholder="Enter kiosk code" />
        </Form.Item>

        {/* Name Field */}
        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: "Please input the kiosk name!" }]}
        >
          <Input placeholder="Enter kiosk name" />
        </Form.Item>

        {/* Version Field */}
        <Form.Item
          name="version"
          label="Version"
          rules={[
            { required: true, message: "Please input the version!" },
            {
              validator(_, value) {
                if (value === undefined || value >= 0) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error("Version cannot be less than 0!")
                );
              },
            },
          ]}
        >
          <Input
            type="number"
            placeholder="Enter version"
            onInput={(e) => {
              const target = e.target as HTMLInputElement;
              if (Number(target.value) < 0) {
                target.value = "0"; // Reset the value to 0 if less than 0 is entered
              }
            }}
            onKeyDown={(e) => {
              if (e.key === "-" || e.key === "e" || e.key === "E") {
                e.preventDefault(); // Prevent negative and exponential entries
              }
            }}
          />
        </Form.Item>

         <Form.Item
            name="edc_store_id"
            label="EDC Store ID"
            rules={[
              { required: true, message: "Please input the EDC Store ID!" },
            ]}
          >
            <Input type="number" placeholder="Enter EDC Store ID" />
          </Form.Item>

        <Form.Item
          name="edc_clientiD"
          label="EDC Client ID"
          rules={[
            { required: true, message: "Please input the EDC Client ID!" },
          ]}
        >
          <Input type="number" placeholder="Enter EDC Client ID" />
        </Form.Item>

        <Form.Item
          name="edc_securitytoken"
          label="EDC Security Token"
          rules={[
            {
              required: true,
              message: "Please input the EDC Security Token!",
            },
          ]}
        >
          <Input type="text" placeholder="Enter EDC Security Token" />
        </Form.Item>

        {/* TeamViewer ID Field */}
        <Form.Item
          name="teamviewer_id"
          label="TeamViewer ID"
          rules={[
            { required: true, message: "Please input the TeamViewer ID!" },
          ]}
        >
          <Input placeholder="Enter TeamViewer ID" />
        </Form.Item>

        {/* AnyDesk ID Field */}
        <Form.Item
          name="anydesk_id"
          label="AnyDesk ID"
          rules={[{ required: true, message: "Please input the AnyDesk ID!" }]}
        >
          <Input placeholder="Enter AnyDesk ID" />
        </Form.Item>

        {/* EDC Serial Number Field
        <Form.Item
          name="edc_serial_number"
          label="EDC Serial Number"
          rules={[
            { required: true, message: "Please input the EDC Serial Number!" },
          ]}
        >
          <Input placeholder="Enter EDC Serial Number" />
        </Form.Item> */}

        {/* Printer Serial Number Field */}
        <Form.Item
          name="printer_serial_number"
          label="Printer Serial Number"
          rules={[
            {
              required: true,
              message: "Please input the Printer Serial Number!",
            },
          ]}
        >
          <Input placeholder="Enter Printer Serial Number" />
        </Form.Item>

        {/* Type Field */}
        <Form.Item
          name="type"
          label="Type"
          rules={[{ required: true, message: "Please select the kiosk type!" }]}
        >
          <Select
            placeholder="Select kiosk type"
            value={selectedKiosk}
            onChange={(value: string) => setSelectedKiosk(value)}
          >
            {kioskTypes.map((kiosk) => (
              <Option key={kiosk.value} value={kiosk.value}>
                {kiosk.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="store_location_type"
          label="Store Location Type"
          rules={[
            {
              required: true,
              message: "Please select the store location type!",
            },
          ]}
        >
          <Select placeholder="Select store location type">
            {STORE_TYPE_CHOICES.map((kiosk) => (
              <Select.Option key={kiosk.value} value={kiosk.value}>
                {kiosk.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Store Field */}
        <Form.Item
          name="store"
          label="Store"
          rules={[{ required: true, message: "Please input the store name!" }]}
        >
          <Input placeholder="Enter store" disabled />
        </Form.Item>

        {/* Is Active Field */}
        <Form.Item name="is_active" label="Is Active" valuePropName="checked">
          <Switch />
        </Form.Item>

        {/* Submit and Cancel Buttons */}
        <Form.Item label={null}>
          <Button className="btn-save" type="primary" htmlType="submit">
            {loading ? `${Saving}` : `${Btn_Update_Kiosk}`}
          </Button>
          <Button className="btn-cancel" onClick={() => navigate(-1)}>
            {Cancel}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default EditKiosk;
