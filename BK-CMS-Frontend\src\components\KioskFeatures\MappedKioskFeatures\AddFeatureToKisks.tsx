import { useState, useCallback } from "react";
import { Form, AutoComplete, message, Button, Spin, Alert, Card } from "antd";
import debounce from "lodash.debounce";
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";

interface KioskResponse {
  id: number;
  store: string;
  store_id: number;
  code: string;
  name: string;
  version: number;
  teamviewer_id: string;
  anydesk_id: string;
  edc_securitytoken: string;
  edc_clientiD: string;
  printer_serial_number: string;
  printer_vendor_id: string;
  printer_product_id: string;
  token: string;
  fcm_token: string;
  type: string;
  store_location_type: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

const AddKioskFeature = () => {
  const [form] = Form.useForm();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<KioskResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedKiosk, setSelectedKiosk] = useState<number | null>(null);
  const [error, setError] = useState("");

  const fetchKiosks = async (search = "") => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(`/cms/stores/kiosks/`, {
        params: { code: search, feature_id: id },
      });
      setSearchResults(response.data.objects);
    } catch (error) {
      console.error("Error fetching kiosks:", error);
      message.error("Failed to fetch kiosks. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const debouncedFetchKiosks = useCallback(
    debounce((value) => fetchKiosks(value), 500),
    []
  );

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    if (value.trim() !== "") {
      debouncedFetchKiosks(value.trim());
    } else {
      setSearchResults([]);
    }
  };

  const handleSelect = (value: string) => {
    console.log("Selected value", value);
    // const kioskId = parseInt(value, 10);
    const selected = searchResults.find(
      (kiosk) => `${kiosk.name} (${kiosk.code})` === value
    );
    console.log("Seleted", selected);
    if (selected) {
      setSelectedKiosk(selected.id);
      setSearchTerm(`${selected.name} (${selected.code})`);

      //  form.setFieldsValue({ kiosk: selected.id });
    }
  };

  const handleSubmit = async () => {
    if (!selectedKiosk) {
      message.warning("Please select a kiosk.");
      return;
    }
    try {
      const payload = { feature: Number(id), kiosk: selectedKiosk };
      const response = await axiosInstance.post(
        `/cms/stores/features-kiosk/${selectedKiosk}/`,
        payload
      );
      if (response.status === 201) {
        message.success("Feature added successfully!");
        setSelectedKiosk(null);
        setSearchTerm("");
        navigate(`/feature-details/${id}/kiosks`);
      }
    } catch (error: any) {
      console.error("Error adding feature:", error);
      setError(error.response.data.error || "Failed to add feature.");
      message.error(
        error.response.data.error ||
          "Failed to add feature. Please try again later."
      );
    }
  };

  return (
    <div className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md w-full max-w-xl mx-auto">
      <div>
        <h2 className="heading-title">Add Features</h2>
      </div>
      <Card className="mt-3" bordered={true}>
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
          onFinish={handleSubmit}
        >
          <Form.Item
            name="kiosk"
            label="Select Kiosk"
            rules={[{ required: true, message: "Please select a kiosk" }]}
            className="w-full"
          >
            <AutoComplete
              placeholder="Search with code or name"
              value={searchTerm}
              onSearch={handleSearchChange}
              onSelect={handleSelect}
              options={searchResults.map((kiosk) => ({
                value: `${kiosk.name} (${kiosk.code})`,
                label: `${kiosk.name} (${kiosk.code})`,
              }))}
              notFoundContent={loading ? <Spin /> : "No results found"}
              allowClear
              //   className="w-full p-5 py-2 px-4 rounded-md border border-gray-300"
            />
          </Form.Item>

          {error && (
            <Alert
              message={error}
              type="warning"
              showIcon
              className="mt-2 text-red-500"
            />
          )}

          <Form.Item label={null}>
            <Button
              type="primary"
              onClick={handleSubmit}
              className="btn-save mt-4 w-full"
            >
              Map Feature to Kiosk
            </Button>

            <Button
              type="default"
              onClick={() => navigate(`/feature-details/${id}/kiosks`)}
              className="btn-cancel mt-4 w-full"
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default AddKioskFeature;
