/* Styles for the StoresList component */

/* .stores-list-container {
  padding: 20px !important;
} */

.stores-list-divider {
  border-color: #7cb305 !important;
  font-weight: bold !important;
  font-size: 20px !important;
}

.stores-list-header {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  margin-bottom: 20px !important;
}

.store-name {
  cursor: pointer !important;
}

.store-name:hover {
  color: darkblue !important;
}

.add-store-button {
  background-color: #007bff !important;
  color: white !important;
  border: none !important;
  padding: 10px 20px !important;
  font-size: 16px !important;
  font-weight: bold !important;
  border-radius: 5px !important;
}

.stores-list-pagination {
  text-align: center !important;
  margin-top: 20px !important;
}

/* filter search input */

.filter-input-code {
  width: 127px !important;
}
