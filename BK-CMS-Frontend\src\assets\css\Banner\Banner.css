.banner-image {
  margin-left: 133px;
  position: relative;
}

/* upload banner image */

.upload-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  border-radius: 5px;
  margin-bottom: 5px;
  height: 40px; /* Fixed height to prevent shifting */
  width: 100%; /* Full width */
  background: #fff; /* Keeps UI clean */
}

.upload-file-name {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%; /* Force alignment */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.upload-button {
  height: 32px; /* Forces button to align properly */
  min-width: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: normal;
}

/* Creative file Modal check-icon */
.check-icon {
  position: absolute;
  top: -10px;
  right: -10px;
  color: #ff8732;
  font-size: 20px;
  background-color: white;
  border-radius: 50%;
  padding: 2px;
}

/* Card type text */
.card-type-text {
  height: 100px;
  overflow: hidden;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 5px;
  text-align: center;
}

/* flex card*/
.left-section{
  flex: 1;
}

/* image and video & Text content */

.video-preview {
  border: 1px solid #ddd;
  width: 200px;
  height: 200px;
  display: block;
  margin: auto;
  margin-bottom: 10px;
}

.image-preview {
  display: block;
  margin: auto;
  width: 200px;
}
