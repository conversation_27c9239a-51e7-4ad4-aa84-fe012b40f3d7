Reset the default margin and padding for body body {
  margin: 0;
  padding: 0;
}

/* Make the navbar fixed at the top of the screen */
.navbar {
  /* top: 0;
    left: 0; 
    width: 100%;
    height: 70px; 
    z-index: 1000;
    padding: 0; */
  width: 100%;
  background: linear-gradient(
    45deg,
    #ff6f61,
    #ffcc00
  ); /* Gradient background */
  color: white;
  height: 90px;
}

/* Align Nav items to the left or right based on preference */
.navbar .ms-auto {
  margin-left: auto !important;
}

/* Main content should be pushed down by the navbar's height */
.main-content {
  padding-top: 80px; /* Ensure there's enough space for the navbar */
}

/* Ensure the Topbar layout works well on smaller screens */
@media (max-width: 768px) {
  .navbar {
    height: 60px; /* Adjust for smaller screen height */
  }

  .main-content {
    padding-top: 70px; /* Adjust the space for smaller screens */
  }
  .sortable tr {
    cursor: pointer;
  }
}
