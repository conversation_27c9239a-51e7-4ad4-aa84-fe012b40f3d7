import { useState } from "react";
import axios from "axios";
import { RcFile } from "antd/es/upload";
import { notification } from "antd";
import { axiosInstance } from "../apiCalls";


interface UseUploadFileResult {
  uploading: boolean;
  uploadProgress: number;
  uploadSuccess: boolean | null;
  fileKey: string;
  contentType: string;
  getPresignedUrlAndUpload: (file: RcFile, fileType: string) => Promise<void>;
  resetUploadState: () => void;
}

export const useUploadFile = (): UseUploadFileResult => {
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadSuccess, setUploadSuccess] = useState<boolean | null>(null);
  const [fileKey, setFileKey] = useState<string>("");
  const [contentType, setContentType] = useState<string>("");

  // Upload to S3
  const uploadToS3 = async (
    presignedUrl: string,
    file: RcFile,
    key: string
  ) => {
    setUploading(true);
    try {
      const response = await axios.put(presignedUrl, file, {
        headers: { "Content-Type": file.type || "application/octet-stream" },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.loaded && progressEvent.total) {
            const percent = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            setUploadProgress(percent);
          }
        },
      });

      if (response.status === 200) {
        setContentType(file.type);
        setFileKey(key);
        setUploadSuccess(true);
        notification.success({
          message: "Upload Successful",
          description: `${file.name} uploaded successfully.`,
        });
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error: unknown) {
        setUploadSuccess(false);
        setUploadProgress(0);
  
        let errorMessage = "Failed to upload file.";

if (axios.isAxiosError(error)) {
  if (error.response) {
    const statusCodeMessages: Record<number, string> = {
      400: "Invalid request. Please check the file format.",
      401: "Unauthorized access. Please log in again.",
      403: "You don't have permission to upload this file.",
      404: "Upload URL not found. Please try again.",
      413: "File size exceeds the allowed limit.",
      500: "Server error. Please try again later.",
      502: "Bad Gateway. Server is temporarily unavailable.",
      503: "Service unavailable. Please try again later.",
      504: "Gateway timeout. Server took too long to respond.",
    };

    // Use predefined message based on status code
    errorMessage = statusCodeMessages[error.response.status] || "An error occurred during upload.";

    // Extract text message if response contains data
    if (typeof error.response.data === "string" && error.response.data.includes("<Message>")) {
      const match = error.response.data.match(/<Message>(.*?)<\/Message>/);
      if (match) {
        errorMessage = match[1]; // Extract only the message part
      }
    }
  } else if (error.request) {
    errorMessage = "No response received from the server. Please try again.";
  } else {
    errorMessage = "Network error. Please check your connection.";
  }
} else if (error instanceof Error) {
  errorMessage = error.message;
}

// Display the error message in a notification
notification.error({
  message: "Upload Failed",
  description: errorMessage,
});

      } finally {
        setUploading(false);
      }
    };

  // Fetch Presigned URL & Upload File
  const getPresignedUrlAndUpload = async (
    file: RcFile,
    fileType: string
  ): Promise<void> => {
    try {
      const response = await axiosInstance.put("/cms/menu/upload_file/", {
        directory: fileType,
        content_type: file.type.startsWith("image") ? "image" : "video",
        source_name: file.name,
      });

      if (response.data.presigned_url && response.data.file_key) {
        await uploadToS3(
          response.data.presigned_url,
          file,
          response.data.file_key
        );
      } else {
        throw new Error("Presigned URL or file key is missing.");
      }
    } catch (error: unknown) {
        let errorMessage = "An unexpected error occurred. Please try again later.";

        if (axios.isAxiosError(error)) {
          if (error.response) {
            // Handle common HTTP errors
            const status = error.response.status;
            if (typeof error.response.data === "string" && error.response.data.includes("<!DOCTYPE html>")) {
              errorMessage = "The requested resource is unavailable or does not exist.";
            } else {
              errorMessage = error.response.data?.message || "An error occurred while processing your request.";
            }
      
            // Customize message based on HTTP status codes
            switch (status) {
              case 400:
                errorMessage = "Bad request. Please check your input and try again.";
                break;
              case 401:
                errorMessage = "Unauthorized access. Please log in to continue.";
                break;
              case 403:
                errorMessage = "Access denied. You do not have permission to perform this action.";
                break;
              case 404:
                errorMessage = "The requested resource could not be found.";
                break;
              case 500:
                errorMessage = "A server error occurred. Please try again later.";
                break;
              default:
                errorMessage = error.response.data?.message || "An error occurred while processing your request.";
            }
          } else if (error.request) {
            errorMessage = "No response from the server. Please check your network connection.";
          } else {
            errorMessage = error.message;
          }
        } else if (error instanceof Error) {
          errorMessage = error.message;
        }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    }
  };

  // Reset Upload State
  const resetUploadState = () => {
    setUploadProgress(0);
    setUploadSuccess(null);
    setFileKey("");
    setContentType("");
  };

  return {
    uploading,
    uploadProgress,
    uploadSuccess,
    fileKey,
    contentType,
    getPresignedUrlAndUpload,
    resetUploadState,
  };
};
