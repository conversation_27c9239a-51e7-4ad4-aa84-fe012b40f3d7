import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Table, Button, notification, Pagination, Input } from "antd";
import { axiosInstance } from "../../apiCalls";
import debounce from "lodash.debounce";
import { Cancel, MAP_STORES } from "../../constants/Constant";

const API_ENDPOINT = "/cms/stores/stores/";

interface Store {
  key: number;
  id: number;
  name: string;
}

const MapStoresToBanner: React.FC = () => {
  const { bannerId } = useParams<{ bannerId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);

  const initialSearch = params.get("search") || "";
  const initialPage = parseInt(params.get("page") || "1", 10);
  const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  const [searchTerm, setSearchTerm] = useState<string>(initialSearch);
  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [storeOptions, setStoreOptions] = useState<Store[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  const updateURLParams = (
    updatedFilters: Record<string, string | number | undefined>
  ) => {
    const params = new URLSearchParams();
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value !== undefined) params.set(key, String(value));
    });
    navigate(`?${params.toString()}`, { replace: true });
  };

  const fetchStores = async (page: number, size: number, search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `${API_ENDPOINT}?page=${page}&page_size=${size}&name=${encodeURIComponent(
          search
        )}`
      );
      if (response.data?.objects) {
        setStoreOptions(
          response.data.objects.map((store: any) => ({
            key: store.id,
            id: store.id,
            name: store.name,
          }))
        );
        setTotalCount(response.data.total_count);
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  const debouncedFetchStores = useCallback(debounce(fetchStores, 500), []);

  useEffect(() => {
    debouncedFetchStores(currentPage, pageSize, searchTerm);
    return () => debouncedFetchStores.cancel();
  }, [currentPage, pageSize, searchTerm]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    setSearchTerm(params.get("search") || "");
    setCurrentPage(parseInt(params.get("page") || "1", 10));
    setPageSize(parseInt(params.get("page_size") || "10", 10));
  }, [location.search]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value; // Keep real-time user input
    setSearchTerm(value);

    // Update URL params for back/forward navigation
    updateURLParams({ search: value, page: 1, page_size: pageSize });

    // Trigger debounced API call for filtering as user types
    debouncedFetchStores(1, pageSize, value);
  };

  const handleStoreMapping = async () => {
    if (!bannerId) {
      notification.error({
        message: "Invalid Banner",
        description: "No banner ID found for mapping.",
      });
      return;
    }
    const selectedIds = Array.from(selectedStores);

    if (selectedIds.length === 0) {
      notification.warning({
        message: "No Stores Selected",
        description: "Please select at least one store to map.",
      });
      return;
    }

    const payload = {
      banner_id: parseInt(bannerId, 10),
      store_ids: selectedIds,
    };

    try {
      await axiosInstance.post("/cms/menu/store-banners/", payload);
      notification.success({
        message: "Stores Mapped",
        description: "Stores have been successfully linked to the banner.",
      });
      navigate("/banners-list");
    } catch (error) {
      console.error("Error mapping stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to map stores to the banner.",
      });
    }
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
    updateURLParams({ search: searchTerm, page, page_size: pageSize });
  };

  const handleSelectAllToggle = (selected: boolean) => {
    setSelectedStores((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        storeOptions.forEach((store) => newSet.add(store.id));
      } else {
        storeOptions.forEach((store) => newSet.delete(store.id));
      }
      return newSet;
    });
  };

  const rowSelection = {
    selectedRowKeys: Array.from(selectedStores),
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedStores((prev) => {
        const newSet = new Set(prev);
        selectedRowKeys.forEach((key) => newSet.add(Number(key)));
        return newSet;
      });
    },
    onSelect: (record: Store, selected: boolean) => {
      setSelectedStores((prev) => {
        const newSet = new Set(prev);
        if (selected) {
          newSet.add(record.id);
        } else {
          newSet.delete(record.id);
        }
        return newSet;
      });
    },
    onSelectAll: (selected: boolean) => {
      handleSelectAllToggle(selected);
    },
  };

  const columns = [
    {
      title: "Select All",
      dataIndex: "",
      key: "",
      width: "10%",
    },
    { title: "Stores", dataIndex: "name", key: "name" },
  ];

  return (
    <div>
      <div className="heading-title">Map Stores to Banner</div>
      <div className="d-flex justify-content-between align-items-center mb-3 mt-4">
        <div className="search-btn-driver">
          <Input.Search
            placeholder="Search stores..."
            value={searchTerm}
            onChange={handleSearchChange}
            allowClear
          />
        </div>
      </div>
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={storeOptions}
        loading={loading}
        pagination={false}
        rowKey="id"
      />
      <div className="ms-5">
        {" "}
        <Button
          className="btn-save"
          type="primary"
          onClick={handleStoreMapping}
          style={{ marginTop: "15px" }}
        >
          {MAP_STORES}
        </Button>
        <Button className="btn-cancel" onClick={() => navigate(-1)}>
          {Cancel}
        </Button>
      </div>

      <div className="pagination">
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={handlePageChange}
          showSizeChanger
        />
      </div>
    </div>
  );
};

export default MapStoresToBanner;
