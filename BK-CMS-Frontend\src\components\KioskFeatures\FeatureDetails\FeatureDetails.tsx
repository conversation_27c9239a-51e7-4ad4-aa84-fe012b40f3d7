import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Typography, Spin, Button, Tabs, Alert } from "antd";

import dayjs from "dayjs";
import "../../../assets/css/Banner/Banner.css";
import { axiosInstance } from "../../../apiCalls";
import { KioskFeaturesType } from "../KioskFeatures";
import MappedKioskFeatures from "../MappedKioskFeatures/MappedKioskFeatures";
import BackButton from "../../UI/BackButton";

const { Text } = Typography;

const FeatureDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [details, setDetails] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);

  const navigate = useNavigate();
  const location = useLocation();

  const detailsMatch = location.pathname === `/feature-details/${id}`;
  const storesMatch = location.pathname === `/feature-details/${id}/kiosks`;

  const tabs = [
    { name: "Feature Details", link: `/feature-details/${id}` },
    { name: "Mapped Kiosks", link: `/feature-details/${id}/kiosks` },
  ];

  const tabItems = tabs.map((tab) => ({
    key: tab.link,
    label: <span className="custom-tab">{tab.name}</span>,
    children: null,
  }));

  const getBannerDetails = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `/cms/stores/features-details/${id}/`
      );
      // console.log("Details", response.data);
      if (response.status === 200) {
        setDetails(response.data);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getBannerDetails();
  }, [id]);

  const formatText = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const renderDetailsInCard = () => {
    if (!details) return null;

    const fields: { label: string; key: keyof KioskFeaturesType }[] = [
      { label: "Name", key: "name" },
      { label: "Code", key: "code" },
      { label: "Status", key: "is_active" },

      { label: "Created Date ", key: "created_at" },
      { label: "Description ", key: "description" },
    ];

    return fields.map((field) => {
      const value = details[field.key];
      let displayValue: React.ReactNode;
      if (field.key === "name" && typeof value === "string") {
        displayValue = formatText(value);
      } else if (field.key === "created_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (typeof value === "boolean") {
        displayValue = value ? "Yes" : "No";
      } else if (value === null || value === undefined) {
        displayValue = "-";
      } else if (typeof value === "object") {
        // Display a placeholder message for objects
        displayValue = "Details available";
      } else {
        displayValue = value; // Handle strings and numbers directly
      }

      return (
        <div key={field.key.toString()} className="order-details-value">
          <div className="order-details-label">{field.label}</div>
          <span className="order-details-value-colon">:</span>
          <span className="order-details-value-value">{displayValue}</span>
        </div>
      );
    });
  };

  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  }
  // if (error) return <Text type="danger">{error}</Text>;
  if (!loading && error) {
    return (
      <div className="error-container">
        <Alert message="Error" description={error} type="error" showIcon />
        <div className="retry-button d-flex justify-content-center align-items-center mt-3">
          <Button type="primary" onClick={() => navigate(-1)}>
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  if (!details) return <Text>No details found</Text>;

  return (
    <>
    <div className="d-flex justify-content-start align-items-center">
        <BackButton to={`/features-list`} />
      </div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Kiosk Feature Details</div>
      </div>
      <div className="w-full max-w-3xl mx-auto m-2">
        {/* Tab Navigation */}
        <div className="tabs-navigation mt-4">
          <Tabs
            activeKey={location.pathname}
            tabBarStyle={{ borderBottom: "none" }}
            onChange={(key) => navigate(key)}
            items={tabItems}
          />
        </div>

        <div className="max-w-3xl w-full">
          {detailsMatch && (
            <div>
              <div className="d-flex justify-content-end align-items-center">
                <Button
                  type="primary"
                  className="btn-save"
                  onClick={() => navigate(`/feature-details/${id}/edit`)}
                >
                  Edit
                </Button>
              </div>
              <div className="banner-details-container d-flex flex-wrap justify-content-between align-items-center">
                {/* Left Side */}
                <div className="left-section" style={{ flex: 1 }}>
                  {renderDetailsInCard()}
                </div>

                {/* <Divider className="divider" type="vertical" /> */}

                {/* Right Side */}
                {/* <div className="right-section" style={{ flex: 1 }}>
                 
                </div> */}
              </div>
              {/* <div className="banner-details-container" style={{ padding: "0px" }}>
        {MappedStores()}
      </div> */}
            </div>
          )}
          {storesMatch && <MappedKioskFeatures />}
        </div>
      </div>
    </>
  );
};

export default FeatureDetails;
