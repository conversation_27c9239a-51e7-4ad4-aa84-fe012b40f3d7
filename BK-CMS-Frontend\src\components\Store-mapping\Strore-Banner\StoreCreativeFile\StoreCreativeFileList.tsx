import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, message, <PERSON><PERSON>, <PERSON><PERSON>, Spin } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";

import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { axiosInstance } from "../../../../apiCalls";
import { handleApiError } from "../../../../utils/ApiErrorHandler";
import { StoreBannerProps } from "../TypeProps/StoreBannerTypeProps";
import { Retry } from "../../../../constants/Constant";
import SelectCreativeFileModal from "./SelectCreativeFiles/SelectCreativeFiles";
import { CreativeFileObject } from "../../../../types/CreativeFileType/CreativeFileType";
import useMetaData from "../../../../hooks/useMetaData";
import DataTable from "../../../shared/DataTable/commonDataTable";
import CommonPagination from "../../../shared/Pagination/commonPagination";

const StoreCreativeFileList = ({ storeId,storeCode }: StoreBannerProps) => {
  const [banners, setBanners] = useState<CreativeFileObject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  //const [isloading, setIsloading] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const { metaData, errors, isloading } = useMetaData();

  const location = useLocation();
  const navigate = useNavigate();

  const [dynamicAttributes, setDynamicAttributes] = useState({
    creative_types: [],
  });

  const params = new URLSearchParams(location.search);
  const initialPage = parseInt(params.get("page") || "1", 10);
  const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [filters, setFilters] = useState<Record<string, string | undefined>>(
    useCallback(() => {
      const filterObj: Record<string, string | undefined> = {};
      params.forEach((value, key) => {
        if (!["page", "page_size"].includes(key)) {
          filterObj[key] = value;
        }
      });
      return filterObj;
    }, [params])
  );

  useEffect(() => {
    const creatives = metaData?.creative_meta;
    if (creatives) {
      setDynamicAttributes(creatives);
    }
  }, [metaData, errors]);

  const fetchBanners = useCallback(
    async (page = currentPage, size = pageSize, filtersParam = filters) => {
      try {
        setLoading(true);
        setError(null);

        const response = await axiosInstance.get(
          `/cms/menu/store-creatives/?store_code=${storeCode}`,
          {
            params: { page, page_size: size, ...filtersParam },
          }
        );

        if (response.status === 200) {
          setBanners(response.data.objects);
          setTotalCount(response.data.total_count);
        } else {
          setError(`Unexpected response status: ${response.status}`);
        }
      } catch (err: unknown) {
        handleApiError(err, setError);
      } finally {
        setLoading(false);
      }
    },
    [storeId, currentPage, pageSize, filters]
  );

  useEffect(() => {
    fetchBanners();
  }, [fetchBanners]);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);

    const updatedFilters = {
      ...filters,
      page: page.toString(),
      page_size: (size || pageSize).toString(),
    };
    setFilters(updatedFilters);
    navigate(`?${new URLSearchParams(updatedFilters).toString()}`);
    fetchBanners(page, size || pageSize, updatedFilters);
  };

  // const handleFilterChange = (key: string, value: string) => {
  //   const updatedFilters =
  //     value === "All" ? { page: "1" } : { ...filters, [key]: value, page: "1" };
  //   setFilters(updatedFilters);
  //   navigate(`?${new URLSearchParams(updatedFilters).toString()}`);
  //   fetchBanners(1, pageSize, updatedFilters);
  // };

  const handleStatusChange = async (id: number, isActive: boolean) => {
    try {
      setLoading(true);

      // Show confirmation before deactivating
      if (!isActive) {
        Modal.confirm({
          title: "Deactivate Type",
          content: "Are you sure you want to deactivate this Type?",
          okText: "Yes",
          cancelText: "No",
          className: "custom-modal", // Apply custom class
          okButtonProps: { className: "custom-modal-ok-button" }, // Apply button class
          cancelButtonProps: { className: "custom-modal-cancel-button" },
          onOk: async () => {
            await updateBannerStatus(id, isActive);
          },
        });
      } else {
        // Allow API to handle validation for activating a banner
        await updateBannerStatus(id, isActive);
      }
    } catch (err) {
      message.error("Error updating Type status.");
    } finally {
      setLoading(false);
    }
  };

  const updateBannerStatus = async (id: number, isActive: boolean) => {
    try {
      const res = await axiosInstance.put(
        `cms/menu/store-creative-details/${id}/`,
        {
          is_active: isActive,
        }
      );
      if (res.status === 200) {
        message.success(res.data.message);
        setBanners((prev) =>
          prev.map((creative) =>
            creative.id === id ? { ...creative, is_active: isActive } : creative
          )
        );
      }
    } catch (err: any) {
      message.error(
        err.response?.data?.message || "Error updating type status."
      );
    }
  };

  // const formatDate = (dateString: string): string => {
  //   return dayjs(dateString).format("DD/MM/YYYY, hh:mm a");
  // };

  const formatText = (text: string) => {
    if (!text && text === "") return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  }

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "30%",
      render: (name: string, record: CreativeFileObject) => (
        <Link
          className="common-link text-decoration-none"
          to={`/store/${storeId}/creative-file-details/${record.creative_id}`}
        >
          {name}
        </Link>
      ),
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      width: "20%",
      render: (name: string) => formatText(name),
    },
    {
      title: "Content Type",
      dataIndex: "content_type",
      key: "content_type",
      width: "20%",
      render: (name: string) => formatText(name),
    },
    {
      title: "Updated Date",
      dataIndex: "updated_at",
      key: "updated_at",
      width: "20%",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },

    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      width: "15%",
      render: (is_active: boolean, record: CreativeFileObject) => (
        <div className="d-flex">
          <div
            className={`switch-button ${is_active ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !is_active)}
          >
            <span className="switch-label">
              {is_active ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  if (!isloading && error) {
    <div className="error-container">
      <Alert message="Error" description={error} type="error" showIcon />
      <div className="retry-button d-flex justify-content-center align-items-center mt-3">
        <Button type="primary" onClick={() => fetchBanners()}>
          {Retry}
        </Button>
      </div>
    </div>;
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Store Creative Files</div>
        <div className="d-flex">
          <Button type="primary" onClick={() => setIsModalVisible(true)}>
            Select Creative Files
          </Button>
        </div>
      </div>
      <div className="mt-3">
        <DataTable<CreativeFileObject>
          bordered
          dataSource={banners}
          columns={columns}
          loading={loading}
          pagination={false}
          rowKey={(record) => record.id.toString()}
          scroll={{ x: "max-content" }}
        />

        <CommonPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={handlePageChange}
        />
      </div>
      <div>
        <SelectCreativeFileModal
          visible={isModalVisible}
          dynamicAttributes={dynamicAttributes}
          onClose={() => setIsModalVisible(false)}
          storeId={storeId || ""}
          refreshData={() => fetchBanners()}
        />
      </div>
    </div>
  );
};

export default StoreCreativeFileList;
