export interface PresignedUrlFields {
  key: string;
  policy: string;
  "x-amz-date": string;
  "x-amz-algorithm": string;
  "x-amz-signature": string;
  "x-amz-credential": string;
}

export interface PresignedUrl {
  url: string;
  fields: PresignedUrlFields;
}

export interface Banner {
  source: string;
  file_key: string;
  content_type: string; // Added directly to the banner
  presigned_url: string;
}

export interface LaunchPage {
  file_key: string;
  content_type: string; // Added directly to the launch page
  presigned_url: string;
}

export interface ServiceOption {
  text: string;
  value: string;
  file_key: string;
  image_url: {
    content_type: string; // Added content type as an object
  };
  presigned_url: string;
}

export interface ServicePage {
  logo: {
    content_type: string; // Updated structure for logo
  };
  image_url: {
    content_type: string; // Updated structure for image_url
  };
  welcome_text: string;
  logo_file_key: string;
  image_file_key: string;
  service_options: ServiceOption[];
}

export interface Data {
  banner?: Banner[];
  launch_page?: LaunchPage[];
  service_page?: ServicePage;
}

export interface Object {
  id: number;
  store_name: string; // Added store name
  code: string;       // Added code field
  name: string | null;
  data: Data;
}

export interface StoreBannerResponse {
  objects: Object[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: string | null;
  previous_page: string | null;
  total_count: number;
}
