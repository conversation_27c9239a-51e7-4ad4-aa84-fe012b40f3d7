import { Alert, Button } from "antd";
import { Retry } from "../../constants/Constant"; // Adjust path as needed

interface ErrorFallbackProps {
  error: string | null;
  onRetry: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, onRetry }) => {
  if (!error) return null;

  return (
    <div className="error-container">
      <Alert message="Error" description={error} type="error" showIcon />
      <div className="retry-button d-flex justify-content-center align-items-center mt-3">
        <Button type="primary" onClick={onRetry}>
          {Retry}
        </Button>
      </div>
    </div>
  );
};

export default ErrorFallback;
