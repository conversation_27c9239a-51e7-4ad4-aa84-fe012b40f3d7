import React from "react";
import { Button } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { Clear_All } from "../../../constants/Constant";

export interface FilterButtonsProps {
  showClearButtons: boolean;
  appliedFilters: string[];
  clearAllFilters?: () => void;
  clearFilter: (key: string) => void;
  formatFilterValue: (value: string) => string;
  filters: Record<string, string | undefined>;
  clearButtonText?: string;
}

const FilterButtons: React.FC<FilterButtonsProps> = ({
  appliedFilters,
  clearAllFilters,
  clearFilter,
  formatFilterValue,
  filters,
}) => {
  const showClearButtonsALL = appliedFilters.length > 1;
  return (
    <div className="d-flex flex-wrap">
      {showClearButtonsALL && (
        <Button type="default" shape="round" onClick={clearAllFilters}>
          {Clear_All}
        </Button>
      )}

      {appliedFilters.map((key) => {
        const filterValue = filters[key];
        const displayValue =
          filterValue === "True"
            ? "Yes"
            : filterValue === "False"
            ? "No"
            : formatFilterValue(filterValue || "");
        return (
          <Button
            key={key}
            type="link"
            shape="round"
            onClick={() => clearFilter(key)}
            className="filter-btn"
          >
            {displayValue}
            <CloseOutlined />
          </Button>
        );
      })}
    </div>
  );
};

export default FilterButtons;
