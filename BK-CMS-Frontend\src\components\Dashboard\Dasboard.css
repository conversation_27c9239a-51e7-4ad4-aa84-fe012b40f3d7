body {
    font-family: <PERSON><PERSON><PERSON>;
    background: #d8d8d8;
  }
  
  * {
    margin: 0px;
    padding: 0px;
  }
  
  .login-container-section {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  
  .login-card {
    background: white;
    padding: 50px;
    border-radius: 30px;
  }
  
  .login-card button.btn.btn-primary {
    background: #2699f7;
    border: none;
    border-radius: 50px;
    width: 100%;
    padding: 15px;
  }
  
  .login-card .form-control {
    border: none;
    border-bottom: 1px solid #c8c8c8;
    border-radius: 0px;
    padding-left: 0px;
    font-size: 14px;
    height: 50px;
  }
  
  .forgot-ps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px !important;
  }
  
  .header-menu-section {
    background: white;
    margin-bottom: 20px;
    height: 60px;
  }
  
  .table-header-section {
    background: #ffffff;
    padding: 15px;
    border-radius: 20px;
  }
  
  .table-header-section th {
    font-weight: 500;
  }
  
  button.btn.btn-primary.btn-off-canvas {
    padding: 0px;
    background: none;
    border: none;
    color: #0d6efd;
    text-decoration: underline;
  }
  
  .table-header-section td {
    border: none;
    border-bottom: 1px solid #0000000f;
    padding: 10px;
    padding-left: 0px;
  }
  
  .header-logo-section {
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: space-between;
  }
  
  .table-header-section h5 {
    border-bottom: 1px solid #2699f71f;
    padding: 12px;
    padding-left: 0px;
  }
  
  .table-header-section th {
    font-weight: 500;
    border: none;
    border-bottom: 1px solid #edeaea;
  }
  
  .profile-section img {
    width: 40px;
    height: 40px;
    border: 1px solid #e9e9e9;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .profile-section p {
    margin: 0px;
  }
  
  .profile-section {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  
  .prev-next {
    display: flex;
    flex-direction: row-reverse;
    margin-top: 10px;
  }
  
  .prev-next button.btn.btn-primary {
    margin-left: 14px;
    border-radius: 30px;
    padding-left: 20px;
    padding-right: 20px;
    background: #2699f7;
    border: none;
  }
  