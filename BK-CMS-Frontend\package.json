{"name": "bk-cms-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "uat": "vite --mode uat", "prod": "vite --mode production", "build:dev": "vite build --mode development", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@hookform/resolvers": "^3.9.1", "antd": "^5.22.5", "antd-style": "^3.7.1", "axios": "^1.7.9", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "cookie": "^1.0.2", "cookies.js": "^2.1.15", "dayjs": "^1.11.13", "formik": "^2.4.6", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lodash.debounce": "^4.0.8", "moment": "^2.29.0", "moment-timezone": "^0.5.46", "rc-picker": "^3.6.0", "react": "^18.3.1", "react-bootstrap": "^2.10.6", "react-dom": "^18.3.1", "react-hook-form": "^7.54.0", "react-router-dom": "^7.0.2", "react-use": "^17.6.0", "reactstrap": "^9.2.3", "zod": "^3.0.0-alpha.19"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/moment": "^2.13.0", "@types/node": "^22.10.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "vite-plugin-static-copy": "^2.2.0"}}