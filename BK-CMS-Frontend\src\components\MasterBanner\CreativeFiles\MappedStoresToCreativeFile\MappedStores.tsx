import React, { useState, useEffect, useMemo, useRef } from "react";
import { useParams, Link } from "react-router-dom";
import { InputRef, Input } from "antd";
import { axiosInstance } from "../../../../apiCalls";
import { CreativeStore } from "../../../../types/CreativeFileType/CreativeFileType";
import CommonPagination from "../../../shared/Pagination/commonPagination";
import DataTable from "../../../shared/DataTable/commonDataTable";
import { useTableFilters } from "../../../../hooks/useTableFilter";
import { SearchOutlined } from "@ant-design/icons";
import FilterButtons from "../../../shared/FilterButton/FilterButton";
import { handleApiError } from "../../../../utils/ApiErrorHandler";
import ErrorFallback from "../../../Error/ErrorPage";

const API_ENDPOINT = "/cms/menu/creatives-detail-stores";

const MappedStores: React.FC = ({}) => {
  const { id } = useParams<{ id: string }>();
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    // clearAllFilters,
  } = useTableFilters();
  // const navigate = useNavigate();
  // const location = useLocation();
  //const params = new URLSearchParams(location.search);

  // const initialPage = parseInt(params.get("page") || "1", 10);
  // const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  // const [currentPage, setCurrentPage] = useState<number>(initialPage);
  // const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [storeOptions, setStoreOptions] = useState<CreativeStore[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  const [error, setError] = useState<string | null>(null);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const codeInputRef = useRef<InputRef>(null);

  const [searchValue, setSearchValue] = useState<string>("");

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const updateURLParams = useCallback(
  //   (updatedFilters: Record<string, string | number | undefined>) => {
  //     const params = new URLSearchParams();
  //     Object.entries(updatedFilters).forEach(([key, value]) => {
  //       if (value !== undefined) params.set(key, String(value));
  //     });
  //     navigate(`?${params.toString()}`, { replace: true });
  //   },
  //   [navigate]
  // );

  // const fetchStores = useCallback(async () => {
  //   setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(`${API_ENDPOINT}/${id}/`, {
  //       params: {
  //         page: currentPage,
  //         page_size: pageSize,
  //       },
  //     });

  //     if (response.data?.objects) {
  //       setStoreOptions(
  //         response.data.objects.map((store: CreativeStore) => ({
  //           key: store.id,
  //           id: store.id,
  //           store_id: store.store_id,
  //           store_name: store.store_name,
  //           store_code: store.store_code,
  //           is_active: store.is_active,
  //         }))
  //       );
  //       setTotalCount(response.data.total_count);
  //     } else {
  //       setStoreOptions([]);
  //       setTotalCount(0);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching stores:", error);
  //     notification.error({
  //       message: "Error",
  //       description: "Failed to load stores.",
  //     });
  //   } finally {
  //     setLoading(false);
  //   }
  // }, [currentPage, pageSize, id]);

  // useEffect(() => {
  //   fetchStores();
  // }, [currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchStores = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get(`${API_ENDPOINT}/${id}/`, {
          params: {
            feature_id: id,
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
          signal: controller.signal,
        });

        if (response.status === 200) {
          setStoreOptions(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  // useEffect(() => {
  //   const params = new URLSearchParams(location.search);

  //   setCurrentPage(parseInt(params.get("page") || "1", 10));
  //   setPageSize(parseInt(params.get("page_size") || "10", 10));
  // }, [location.search]);

  // const handlePageChange = (page: number, pageSize?: number) => {
  //   setCurrentPage(page);
  //   setPageSize(pageSize || 10);
  //   updateURLParams({ page, page_size: pageSize || 10 });
  // };

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  // const clearAllFiltersHandler = () => {
  //   clearAllFilters();
  //   setSearchValue("");
  // };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.toLowerCase() // just lowercase, no uppercase for first letter
        )
        .join(" ")
    );
  };

  const handleFocus = () => {
    if (!isFocused) {
      setIsFocused(true);
      codeInputRef.current?.focus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const columns = [
    {
      title: "Stores",
      dataIndex: "store_name",
      key: "store_name",
      width: "30%",
      render: (text: string, record: CreativeStore) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.store_id}/details`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    {
      title: "Store Code",
      width: "30%",
      dataIndex: "store_code",
      key: "store_code",
    },
    {
      title: "Active Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean) => (isActive ? "Yes" : "No"),
    },
  ];

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <>
      <div>
        <div className="d-flex flex-wrap align-items-center mb-3 mt-2">
          <div className="d-flex flex-wrap align-items-center">
            <div className="search-btn-driver">
              <Input.Search
                ref={codeInputRef}
                placeholder="Search by code & name"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onSearch={(value) => {
                  if (value.trim()) {
                    handleSearchChange(value);
                  }
                }}
                onFocus={handleFocus}
                onBlur={handleBlur}
                prefix={
                  <SearchOutlined
                    // style={{ visibility: isFocused ? "visible" : "hidden" }}
                    className={isFocused ? "" : "invisible"}
                  />
                }
              />
            </div>
            <div>
              <FilterButtons
                showClearButtons={showClearButtons}
                appliedFilters={appliedFilters}
                clearFilter={clearFilterHandler}
                formatFilterValue={formatPaymentMethod}
                filters={filters}
              />
            </div>
          </div>
        </div>

        <div className="mt-4">
          <DataTable<CreativeStore>
            columns={columns}
            dataSource={storeOptions}
            loading={loading}
            pagination={false}
            rowKey="id"
          />

          <div className="pagination">
            <CommonPagination
              current={currentPage}
              pageSize={pageSize}
              total={totalCount}
              onChange={handlePageChange}
              showSizeChanger
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default MappedStores;
