body {
  font-family: "Poppins", serif !important;
}
ul {
  font-family: "Poppins", serif !important;
}
ul li {
  font-family: "Poppins", serif !important;
}

.layout {
  min-height: 100vh;
}

.layout-header {
  display: flex;
  align-items: center;
  background: white;
  padding: 0 16px;
  height: 75px;
  border-bottom: 1px solid #f1f1f1;
}

.menu-icon {
  font-size: 24px;
  cursor: pointer;
  margin-right: 16px;
}

.logo {
  height: 60px;
  object-fit: contain;
}

.logout-button {
  background: #ff9900;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 20px 20px !important;
  cursor: pointer;
  font-size: 20px;
  font-weight: 500;
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  height: 40px;
  line-height: 40px;
  margin-right: 20px;
}
.logout-button:hover {
  color: #fff !important;
  background: #ff7f00 !important;
}

.layout-sider {
  position: fixed;
  left: 6px;
  top: 77px;
  bottom: 0;
  overflow: auto;
  border-radius: 6px !important;
  background: #ff8732 !important;
  /* border-radius: 20px !important; */
  padding: 15px;
  height: 87vh !important;
  padding-left: 4px;
  padding-right: 10px;
  border: none !important;
}

.layout-sider .ant-menu {
  border-inline-end: none !important;
}
.layout-content {
  margin-left: 200px;
  padding: 16px;
  overflow: auto;
  height: calc(100vh - 64px);
  background-color: #ffffff;
  margin-bottom: 30px;
}

.collapsed-content {
  transition: margin-left 0.3s ease, width 0.3s ease;
  margin-left: 60px;
  width: 100%;
}

.expanded-content {
  margin-left: 209px; /* Match the Sider's width */
}

:where(.css-dev-only-do-not-override-1kf000u).ant-menu-light,
:where(.css-dev-only-do-not-override-1kf000u).ant-menu-light > .ant-menu {
  all: unset !important; /* Remove all applied styles */
}

:where(.css-dev-only-do-not-override-1kf000u).ant-layout-sider
  .ant-layout-sider-trigger {
  position: static !important; /* Reset position */
  background: none !important; /* Remove background color */
  height: auto !important; /* Reset height */
  line-height: normal !important; /* Reset line-height */
  display: none !important; /* Hide the trigger completely if not needed */
}
ul.ant-menu {
  background: none;
}
ul.ant-menu.ant-menu-root.ant-menu-inline.ant-menu-light.layout-menu.css-dev-only-do-not-override-1kf000u {
  background: none;
}
li.ant-menu-item span {
  padding-left: 0px !important;
  color: white;
}
li.ant-menu-item.ant-menu-item-selected {
  background: #eb7726;
}
table {
  border-radius: 4px !important;
  /* overflow: hidden !important; */
  border: 1px solid #dddddd;
}
td.ant-table-cell {
  border: none !important;
}
ul li {
  padding-left: 10px !important;
}
.ant-menu-submenu-title {
  padding-left: 0px !important;
  color: white !important;
}
.ant-table-filter-column {
  font-family: "Poppins";
}
td.ant-table-cell {
  font-family: "Poppins";
  font-size: 14px !important;
}
/* card boder */
/* .ant-card.ant-card-bordered.css-1kf000u {
  border: none !important;
} */

/* card edit config page */
.custom-card-edit-config {
  width: 100%;
  max-width: 500px;
  border: 1px solid #d9d9d9 !important;
  background: #ffffff;
  padding: 20px;
  /* box-shadow: 0px 4px 10px rgba(130, 101, 101, 0.1); */
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  border-bottom: 2px solid #ff9900;
  display: inline-block;
  padding-bottom: 5px;
}

.input-width {
  width: 100%;
  max-width: 200px;
}

/* common card render in details page */
.order-details-value {
  font-family: "Poppins", serif !important;
}
.order-details-value {
  margin: 5px;
  padding: 10px;
  padding-left: 0px;
}
.order-details-value {
  display: flex;
}
.order-details-label {
  width: 80%;
}
.order-details-value .order-details-value-colon {
  width: 20px;
}
.order-details-value .order-details-value-value {
  width: 50%;
}
/* .ant-card-body {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
} */
.order-details-value {
  display: flex;
  /* width: 20%; */
  width: 100%;
}
.order-details-label {
  width: 20%;
}
td.ant-table-cell span {
  /* background: none !important; */
  border: none;
  box-shadow: none !important;
  font-family: "Poppins";
}
td.ant-table-cell span {
  /* background: none !important; */
  border: none;
  box-shadow: none !important;
  font-family: "Poppins";
  font-weight: normal !important;
  font-size: 12px !important;
  /* text-transform: lowercase !important; */
}
.search-btn-driver {
  position: relative;
  flex-grow: 1; /* Makes it flexible */
  min-width: 150px; /* Prevents elements from becoming too small */
  max-width: 300px; /* Prevents excessive stretching */
  position: relative;
}

.search-btn-driver .ant-input-affix-wrapper {
  padding: 0 12px; /* Adjust padding to avoid misalignment */
  font-size: 14px; /* Match the font size for proper cursor alignment */
  border: 1px solid #d9d9d9;
  border-radius: 50px 0 0 50px !important; /* Rounded corners only on the left side */
  height: 40px; /* Ensure consistent height */
  box-sizing: border-box; /* Include padding and border in width/height calculations */
  display: flex;
  align-items: center; /* Vertically align the cursor and text */
  font-family: "Arial", sans-serif !important; /* Set font-family */
}

.search-btn-driver input {
  padding: 10px;
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 50px;
  font-family: "poppins", sans-serif !important;
}

.search-btn-driver button {
  height: 40px !important; /* Match the input field height */
  border: 1px solid #d9d9d9 !important;
  width: 55px !important;
  border-radius: 0 50px 50px 0; /* Rounded corners only on the right side */
}

.search-btn-driver svg {
  fill: #ff8732; /* Set custom color for the search icon */
}

.date-btn-driver {
  flex-grow: 1; /* Makes it responsive */
  min-width: 250px;
  max-width: 400px;
}
.date-btn-driver .ant-picker {
  padding: 10px;
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 50px;
}
.search-btn-driver button.ant-btn {
  border-radius: 60px;
  border-left: 2px !important;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* search icone */
.search-container {
  width: 35%;
  position: relative;
}

.search-container .ant-input {
  padding-left: 15px; /* Default padding when icon is hidden */
  padding-right: 15px;
  font-size: 14px;
  height: 45px;
  border-radius: 50px;
  border: 1px solid #d9d9d9;
  box-sizing: border-box;
  transition: all 0.3s ease-in-out;
}

.search-container .ant-input:focus {
  border-color: #ff0000; /* YouTube red */
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
  padding-left: 40px !important; /* Expand padding when icon appears */
}

.search-container .search-icon {
  font-size: 18px;
  color: #ff0000; /* YouTube red */
}

.heading-title {
  font-size: 22px;
  padding: 10px;
  border-bottom: 1px solid #ff7f00;
  min-height: 50px;
  border-radius: 8px 8px 0 0;
  transition: background-color 0.3s ease, color 0.3s ease;
  padding: 4px 16px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.title-and-add-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px; /* Adds spacing between elements */
}

.title-and-add-btn button {
  background: #ff8732 !important;
  height: 40px;
  font-family: "Poppins", sans-serif;
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  border: none;
  color: white;
  flex-shrink: 0; /* Prevents buttons from shrinking */
}

/* switch Button */
.d-flex {
  display: flex;
  align-items: center;
  gap: 15px;
}

.switch-button {
  width: 70px !important;
  height: 34px !important;
  background-color: #d9d9d9 !important;
  border-radius: 17px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  transition: background-color 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  padding: 0 10px;
  font-size: 14px;
  font-weight: bold;
  color: white;
  overflow: hidden;
}

.switch-button .switch-handle {
  width: 28px;
  height: 28px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 3px;
  left: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}

.switch-button.checked {
  background-color: #ff8732 !important;
}

.switch-button.checked .switch-handle {
  transform: translateX(36px);
}

.switch-label {
  position: absolute;
  width: 100%;
  text-align: center;
  z-index: 1;
  font-size: 14px;
  font-weight: bold;
  color: black;
  cursor: pointer;
}

.switch-button.checked .switch-label {
  right: 10px;
  color: white;
  font-family: "Poppins", sans-serif;
}

.switch-button:not(.checked) .switch-label {
  left: 10px;
  color: white;
  font-family: "Poppins", sans-serif;
}

/* switch button pop modal button Yes and No */
.custom-modal .ant-modal-content {
  border-radius: 8px;
  padding: 16px;
}

.custom-modal .ant-modal-header {
  background-color: #ff8732;
  color: white;
  border-radius: 8px 8px 0 0;
}

.custom-modal .ant-modal-title {
  color: white;
  font-weight: bold;
}

.custom-modal-ok-button {
  background-color: #ff8732 !important;
  border-color: #ff8732 !important;
  color: white !important;
}

.custom-modal-cancel-button {
  background-color: #f0f0f0 !important;
  color: black !important;
}

.add-item-bt {
  background: #ff8732 !important;
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  margin-top: 30px;
}
.button-back button {
  position: absolute;
  z-index: 9999;
  background: #ff8732;
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Poppins";
}
main.ant-layout-content {
  background: #ffffff;
  margin-top: 2px;
  border-radius: 4px;
  height: 87vh;
}
.store-list-details {
  width: 100%;
}
.store-list-details {
  display: flex;
}
.store-list-details-s {
  width: 60%;
}
.store-list-details {
  display: flex;
  padding: 12px;
  font-size: 14px;
  font-family: "Poppins";
}
.store-list-details-s {
  width: 20%;
  font-weight: 600;
}
.stores-list-header button {
  background: #ff8732 !important;
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
}
.ant-table-container {
  background: none !important;
  border: none !important;
}

.banner-sections-one {
  display: flex;
  width: 100%;
  /* width: 50%; */
}
.image1 {
  width: auto;
  margin: 20px;
  margin-left: 0px;
  margin-top: 0px;
}
.image1 img {
  height: 280px !important;
  object-fit: cover;
}
.services-images-blocks img {
  height: 350px !important;
  object-fit: cover;
  width: 100% !important;
}
.services-images-blocks {
  width: 100% !important;
  display: flex !important;
}
video {
  width: 25%;
}
/* th.ant-table-cell {
  width: 16%;
} */

.table-container {
  position: relative;
  padding-top: 40px;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1; /* Ensure the loader is above the table */
}

.table-with-loader {
  position: relative;
}

/* pagination styles */

.pagination {
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 10px;
  display: flex;
  justify-content: end;
  align-items: center;
}

/* tabs in store details */
.tabs-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* {filter icon for table} */

.filter-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.3s, color 0.3s;
}

.filter-icon.active {
  background-color: #1677ff;
  color: #fff;
}

.filter-icon.inactive {
  background-color: transparent;
  color: #1890ff;
}

/* order details Item table */

.table-row-even {
  background-color: #fff;
}

.table-row-odd {
  background-color: #f9f9f9;
}

/* order details Tabs */
/* Ensure active tab has an orange bottom border */
.ant-tabs-tab {
  position: relative;
  font-family: "Poppins";
}
.ant-tabs-tab span {
  font-family: "Poppins";
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ff8732 !important; /* Make active tab text orange */
}

.ant-tabs-ink-bar {
  background-color: #ff8732 !important; /* Ensures ink-bar is orange */
  height: 2px !important;
}
.store-name-text {
  margin-bottom: 16px;
  display: inline-block;
  font-size: 18px;
  font-family: Poppins, sans-serif;
}

/* commom link styles */
.common-link {
  color: #ff9900;
  /* padding: 5px 10px; */
  text-decoration: none;
  display: inline-block;
}
.common-link:hover {
  text-decoration: underline;
  color: #ff9900;
}

/* login button */

.btn-login {
  background: #ff8732 !important; /* Save button color */
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  border: none;
  color: white;
}

/* save button styles */
.btn-save {
  background: #ff8732 !important; /* Save button color */
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  border: none;
  color: white;
}

.btn-save:hover {
  background: #e76e1e !important; /* Darker shade on hover */
  color: white;
}

.btn-save:disabled {
  background-color: rgba(
    0,
    0,
    0,
    0.06
  ) !important; /* Light gray with transparency */
  color: rgba(0, 0, 0, 0.3) !important; /* Dimmed text */
  border: 1px solid rgba(0, 0, 0, 0.1) !important; /* Light border */
  cursor: not-allowed;
  opacity: 0.7; /* Softened appearance */
}

.btn-cancel {
  background: #fff !important; /* Grayish color for cancel */
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-left: 20px;
  margin-bottom: 6px;
  border: 1px solid #d9d9d9;
  color: black;
}

.btn-cancel:hover {
  background: #f0f0f0 !important; /* Darker gray on hover */
  color: black;
}

.btn-cancel:focus {
  background: #6c757d !important;
  box-shadow: none !important;
}

.btn-edit-pencil svg {
  font-size: 20px;
  color: #ff8732;
}

/* loader container styles */
.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
}

/* Modal styles */
:where(.css-dev-only-do-not-override-1kf000u).ant-modal .ant-modal-content {
  margin-top: -80px !important;
}

.modal-custom-scroll {
  max-height: 500px;
  background-color: #fff;
  overflow: auto;
  padding: 10px;
  border-radius: 8px;

  scrollbar-width: thin;
  scrollbar-color: #d1d1d1 #f8f9fa;
}

/* logout dropdown styles */

.logout-dropdown {
  font-size: 18px;
  font-weight: 500;
  margin-left: auto;
  letter-spacing: 0.5px;
  height: 40px;
  line-height: 30px;
  margin-right: 20px;
  max-width: 100%;
}

/* Trigger Button */
.dropdown-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  /* border: 1px solid #ff8732; */
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-trigger.active {
  border: none;
}

/* Avatar */
.user-avatar {
  background-color: #ff8732 !important;
  margin-right: 8px;
  flex-shrink: 0;
}

/* Username */
.username {
  font-family: Poppins, sans-serif;
  font-weight: 00;
  flex: 1;
}

/* Dropdown Arrow */
.dropdown-icon {
  margin-left: 20px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.dropdown-icon.rotate {
  transform: rotate(180deg);
}

/* Logout Item */
.logout-text {
  color: red;
}

.logout-icon {
  color: red !important;
}
