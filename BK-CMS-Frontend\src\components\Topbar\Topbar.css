/* Custom navbar styling */
.custom-navbar {
  background: linear-gradient(45deg, #ff6f61, #ffcc00); /* Gradient background */
  height: 70px;
}

/* Navbar items */
.navbar-nav .nav-item {
  font-weight: 500;
  font-size: 1rem;
  color: #fff;
  padding: 10px 20px;
  transition: all 0.3s ease-in-out;
}

.navbar-nav .nav-item:hover {
  color: #ffd700; /* Gold color on hover for a professional feel */
  text-decoration: underline;
}

button.logout-button {
  font-weight: 600;
  color: #fff; /* Red color for the logout button */
  border-color: #fff;
  transition: all 0.3s ease-in-out;
  margin-left: 15px;
}

button.logout-button:hover {
  background-color: red;
  color: #fff;
  border-color: #ffcc00;
}

/* Main page container */
.page-container {
  background-color: #f4f4f9; /* Light grey background */
  min-height: 100vh;
  padding-top: 80px;
}

/* Card-like layout for content */
.card-container {
  background-color: #ffffff; /* White background for the cards */
  border-radius: 8px;
  padding: 30px;
  margin-top: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd; /* Light border for better structure */
}

/* Mobile and tablet adjustments */
@media (max-width: 768px) {
  /* Navbar adjustments */
  .custom-navbar {
    height: auto;
  }

  .navbar-nav .nav-item {
    font-size: 1rem;
    padding: 8px 15px;
  }

  .navbar-toggler {
    border-color: #fff;
  }

  .navbar-toggler-icon {
    background-color: #fff;
  }

  /* Page adjustments for mobile */
  .page-container {
    padding-top: 70px;
  }

  .card-container {
    padding: 20px;
  }
}

/* Very small screen adjustments */
@media (max-width: 576px) {
  .navbar-nav {
    text-align: center;
  }

  .navbar-nav .nav-item {
    font-size: 1.1rem;
  }

  .card-container {
    padding: 15px;
  }

  button.logout-button {
    font-size: 0.85rem;
    margin-left: 10px;
  }
}
