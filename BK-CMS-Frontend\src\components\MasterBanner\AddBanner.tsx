import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  notification,
  Upload,
  Progress,
  Row,
  Col,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../apiCalls";
import axios from "axios";
import { RcFile } from "antd/es/upload";
import { useNavigate } from "react-router-dom";

const AddBanner: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const [uploading, setUploading] = useState<boolean>(false);
  const [fileKeys, setFileKeys] = useState<Record<string, string>>({});
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {}
  );
  const [uploadSuccess, setUploadSuccess] = useState<Record<string, boolean>>(
    {}
  );

  // Store selected files
  const [bannerFile, setBannerFile] = useState<RcFile | null>(null);
  const [logoFile, setLogoFile] = useState<RcFile | null>(null);
  const [imageFile, setImageFile] = useState<RcFile | null>(null);
  const [dineInFile, setDineInFile] = useState<RcFile | null>(null);
  const [takeawayFile, setTakeawayFile] = useState<RcFile | null>(null);
  const [launchPageFiles, setLaunchPageFiles] = useState<RcFile[]>([]);

  // const [bannerId, setBannerId] = useState<string>("");

  const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 5MB
  const MAX_VIDEO_SIZE = 50 * 1024 * 1024; // 50MB

  const ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/jpg"];
  const ALLOWED_VIDEO_TYPES = ["video/mp4"];

  const beforeUpload = (
    file: RcFile,
    isLaunchPage: boolean = false
  ): boolean => {
    const isImage = ALLOWED_IMAGE_TYPES.includes(file.type);
    const isVideo = ALLOWED_VIDEO_TYPES.includes(file.type);

    // If it's a launch page file, allow both images and videos
    if (isLaunchPage) {
      if (!isImage && !isVideo) {
        notification.error({
          message: "Invalid File Type",
          description:
            "Only JPG, PNG, JPEG images, and MP4 videos are allowed.",
        });
        return false;
      }
    } else {
      // Other fields should allow only images
      if (!isImage) {
        notification.error({
          message: "Invalid File Type",
          description: "Only JPG, PNG, and JPEG images are allowed.",
        });
        return false;
      }
    }

    // Validate file size
    const isValidSize = isImage
      ? file.size <= MAX_IMAGE_SIZE
      : file.size <= MAX_VIDEO_SIZE;

    if (!isValidSize) {
      notification.error({
        message: "File Too Large",
        description: isImage
          ? "Images must be less than 10MB."
          : "Videos must be less than 50MB.",
      });
      return false;
    }

    return true; // Valid file
  };

  // Upload to S3 using presigned URL
  const uploadToS3 = async (
    presignedUrl: string,
    file: RcFile,
    key: string
  ) => {
    setUploading(true);
    try {
      await axios.put(presignedUrl, file, {
        headers: { "Content-Type": file.type || "application/octet-stream" },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.loaded && progressEvent.total) {
            const percent = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            setUploadProgress((prev) => ({ ...prev, [key]: percent }));
          }
        },
      });

      setUploadSuccess((prev) => ({ ...prev, [key]: true }));
      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });
    } catch (error) {
      setUploadSuccess((prev) => ({ ...prev, [key]: false }));
      setUploadProgress((prev) => ({ ...prev, [key]: 0 }));
      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };

  // Get presigned URL and upload file
  const getPresignedUrl = async (
    file: RcFile | null,
    key: string,
    directory: string
  ) => {
    if (!file) {
      notification.error({
        message: "No File Selected",
        description: `Please select a ${key} file before uploading.`,
      });
      return;
    }

    try {
      const response = await axiosInstance.put("/cms/menu/upload_file/", {
        directory,
        content_type: file.type.startsWith("image") ? "image" : "video",
        source_name: file.name,
      });

      if (response.data.presigned_url && response.data.file_key) {
        setFileKeys((prev) => ({ ...prev, [key]: response.data.file_key }));
        await uploadToS3(response.data.presigned_url, file, key);
      } else {
        throw new Error("Presigned URL or file key is missing.");
      }
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
    }
  };

  // Submit form
  const handleAddBanner = async (values: any) => {
    if (!Object.keys(uploadSuccess).every((key) => uploadSuccess[key])) {
      notification.error({
        message: "Upload Required",
        description: "Please upload all required files before submitting.",
      });
      return;
    }

    const payload = {
      name: values.name,
      data: {
        banner: [{ source: fileKeys.banner, content_type: "image" }],
        launch_page: launchPageFiles.map((file, index) => ({
          source: fileKeys[`launch_page_${index}`],
          content_type: file.type.startsWith("image") ? "image" : "video",
        })),
        service_page: {
          logo: fileKeys.logo,
          image_url: fileKeys.image,
          welcome_text: values.welcome_text,
          background_color: values.background_color,
          service_options: [
            { text: "Dine - In", value: "dine_in", image_url: fileKeys.dineIn },
            {
              text: "Takeaway",
              value: "take_away",
              image_url: fileKeys.takeaway,
            },
          ],
        },
      },
    };

    try {
      const response = await axiosInstance.post("/cms/menu/banners/", payload);
      if (response.status === 201 || response.status === 200) {
        const bannerId = response.data.banner_id;
        

        if (bannerId) {
          notification.success({
            message: "Banner Added",
            description: "Banner uploaded successfully. Redirecting...",
          });

          setTimeout(() => navigate(`/map-stores-to-banner/${bannerId}`), 1000); // Ensure navigation works
        } else {
          throw new Error("Banner ID missing in response");
        }
      } else {
        throw new Error("Unexpected API response");
      }
    } catch (error) {
      notification.error({
        message: "Error",
        description: "Failed to add banner.",
      });
    }
  };

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Add Banner</div>
      </div>

      <div className="pt-4 mt-3">
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          style={{ maxWidth: 600, marginTop: 20 }}
          onFinish={handleAddBanner}
        >
          <Form.Item
            label="Banner Name"
            name="name"
            rules={[{ required: true, message: "Please enter Name" }]}
          >
            <Input placeholder="Enter Name" />
          </Form.Item>

          <Form.Item label="Welcome Text" name="welcome_text">
            <Input placeholder="Enter Welcome Text" />
          </Form.Item>

          <Form.Item label="Background Color" name="background_color">
            <Input placeholder="Enter Background Color" />
          </Form.Item>

          {/* Upload Fields */}
          {[
            {
              label: "Banner Image",
              fileState: bannerFile,
              setFileState: setBannerFile,
              key: "banner",
            },
            {
              label: "Logo",
              fileState: logoFile,
              setFileState: setLogoFile,
              key: "logo",
            },
            {
              label: "Image",
              fileState: imageFile,
              setFileState: setImageFile,
              key: "image",
            },
            {
              label: "Dine-In Image",
              fileState: dineInFile,
              setFileState: setDineInFile,
              key: "dineIn",
            },
            {
              label: "Takeaway Image",
              fileState: takeawayFile,
              setFileState: setTakeawayFile,
              key: "takeaway",
            },
          ].map(({ label, fileState, setFileState, key }) => (
            <Form.Item key={key} label={label}>
              <Row className="d-flex align-items-center">
                <Col md={8}>
                  <Upload
                    beforeUpload={(file) => {
                      if (!beforeUpload(file)) return false; // Show error immediately
                      setFileState(file);
                      return false;
                    }}
                    maxCount={1}
                    showUploadList={true}
                  >
                    <Button disabled={uploadSuccess[key]}>
                      <UploadOutlined /> Select File
                    </Button>
                  </Upload>
                </Col>
                <Col md={4} className="text-end">
                  <Button
                    type="primary"
                    onClick={() => getPresignedUrl(fileState, key, key)}
                    disabled={!fileState || uploadSuccess[key]}
                  >
                    Upload
                  </Button>
                </Col>
              </Row>
              {uploadProgress[key] > 0 && (
                <Progress percent={uploadProgress[key]} size="small" />
              )}
            </Form.Item>
          ))}

          <Form.Item label="Launch Page (Max 5)">
            <Upload
              multiple
              fileList={launchPageFiles}
              onRemove={(file) => {
                setLaunchPageFiles((prev) =>
                  prev.filter((f) => f.uid !== file.uid)
                );
              }}
              beforeUpload={(file) => {
                if (launchPageFiles.length >= 5) {
                  notification.error({
                    message: "Max Limit Reached",
                    description: "You can only upload up to 5 files.",
                  });
                  return false;
                }
                if (!beforeUpload(file, true)) return false; // Show error immediately
                setLaunchPageFiles((prev) => [...prev, file]);
                return false;
              }}
              maxCount={5}
              showUploadList={false}
            >
              <Button
                icon={<UploadOutlined />}
                disabled={launchPageFiles.length >= 5}
              >
                Select Files
              </Button>
            </Upload>
          </Form.Item>

          {/* Display Selected Files with Perfect Alignment */}
          {launchPageFiles.map((file, index) => (
            <Form.Item
              key={file.uid}
              label={index === 0 ? " " : " "}
              colon={false}
            >
              <Row gutter={16} align="middle" className="upload-container">
                <Col span={18}>
                  <div className="upload-file-name">{file.name}</div>
                </Col>
                <Col span={6} style={{ textAlign: "right" }}>
                  {!uploadSuccess[`launch_page_${index}`] && (
                    <Button
                      type="primary"
                      className="upload-button"
                      onClick={() =>
                        getPresignedUrl(
                          file,
                          `launch_page_${index}`,
                          "launch_page"
                        )
                      }
                      disabled={
                        uploadSuccess[`launch_page_${index}`] ||
                        !launchPageFiles.some((f) => f.uid === file.uid)
                      }
                    >
                      Upload
                    </Button>
                  )}
                </Col>
              </Row>
              {uploadProgress[`launch_page_${index}`] > 0 && (
                <Progress
                  percent={uploadProgress[`launch_page_${index}`]}
                  size="small"
                />
              )}
            </Form.Item>
          ))}

          <Form.Item label={null}>
            <Form.Item>
              <Button type="primary" htmlType="submit" disabled={uploading}>
                Add Banner
              </Button>
            </Form.Item>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default AddBanner;
