import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Form,
  Input,
  Button,
  Select,
  Checkbox,
  InputNumber,
  message,
  Spin,
} from "antd";
import { axiosInstance } from "../../apiCalls";
import { StoreFormData } from "../../types/StoreType/StoreType";
import { timezones } from "../../utils/TimeZoneList"; // List of timezones
import { Cancel, Edit_Store, Save, Saving } from "../../constants/Constant";

const { Option } = Select;

const EditStore: React.FC = () => {
  const [form] = Form.useForm(); // Create the form instance
  const navigate = useNavigate();
  const { storeId } = useParams<{ storeId: string }>();
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const controller = new AbortController();

    // Use an immediately invoked async function inside useEffect.
    (async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get(
          `/cms/stores/store-details/${storeId}/`,
          { signal: controller.signal }
        );
        if (response.status === 200) {
          const storeData = response.data;
          // Set form fields using the API data.
          form.setFieldsValue({
            ...storeData,
            timezone: parseFloat(storeData.timezone), // Convert timezone to number.
          });
        } else {
          message.error("Failed to fetch store details");
        }
      } catch (error: any) {
        // If the error is not due to cancellation, handle it.
        if (error.name !== "CanceledError") {
          console.error("Error fetching store details:", error);
          message.error(
            error.response?.data?.message || "Failed to fetch store details"
          );
        }
      } finally {
        setLoading(false);
      }
    })();

    // Cleanup: cancel the request if the component unmounts.
    return () => {
      controller.abort();
    };
  }, [form, storeId]);

  const onFinish = async (values: StoreFormData) => {
    try {
      setLoading(true);
      const formattedValues = {
        ...values,
        timezone:
          typeof values.timezone === "number"
            ? values.timezone.toFixed(1)
            : values.timezone,
      };

      const response = await axiosInstance.patch(
        `/cms/stores/update-store/${storeId}/`,
        formattedValues
      );

      if (response.status === 200) {
        message.success("Store updated successfully!");
        navigate(`/stores/${storeId}/details`);
      } else {
        message.error("Failed to update store");
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || "Failed to update store");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>{Edit_Store}</div>
      </div>
      <div className="pt-4 mt-4">
        {/* Always render the Form component so that the form instance is connected */}
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 14 }}
          className="mt-2"
          style={{ maxWidth: "600px" }}
          autoComplete="off"
          onFinish={onFinish}
        >
          {loading ? (
            <div className="d-flex justify-content-center align-items-center h-100">
              <Spin size="large" />
            </div>
          ) : (
            <>
              {/* Store Name */}
              <Form.Item
                name="name"
                label="Store Name"
                rules={[{ required: true, message: "Please enter store name" }]}
              >
                <Input placeholder="Enter store name" />
              </Form.Item>

              {/* Store Code */}
              <Form.Item
                name="code"
                label="Store Code"
                rules={[{ required: true, message: "Please enter store code" }]}
              >
                <Input placeholder="Enter store code" />
              </Form.Item>

              <Form.Item
                name="ato_id"
                label="ATO ID"
                rules={[{ required: true, message: "Please enter ATO ID" }]}
              >
                <Input placeholder="Enter ATO ID" />
              </Form.Item>

              {/* <Form.Item
                name="edc_store_id"
                label="EDC Store ID"
                rules={[
                  { required: true, message: "Please enter EDC Store ID" },
                ]}
              >
                <Input type="number" placeholder="Enter EDC Store ID" />
              </Form.Item> */}

              <Form.Item
                name="edc_merchant_id"
                label="EDC Merchant ID"
                rules={[
                  { required: true, message: "Please enter EDC Merchant ID" },
                ]}
              >
                <Input type="number" placeholder="Enter EDC Merchant ID" />
              </Form.Item>

              {/* Timezone */}
              <Form.Item
                name="timezone"
                label="Timezone"
                rules={[
                  { required: true, message: "Please select a timezone" },
                ]}
              >
                <Select placeholder="Select Timezone">
                  {timezones.map((tz) => (
                    <Option key={tz.value} value={tz.value}>
                      {tz.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {/* Phone */}
              <Form.Item
                name="phone"
                label="Phone"
                rules={[
                  { required: true, message: "Please enter a phone number" },
                  {
                    pattern: /^[0-9]{10}$/, // Regex to allow exactly 10 digits
                    message: "Phone number must be exactly 10 digits",
                  },
                ]}
              >
                <Input
                  type="phone"
                  maxLength={10}
                  placeholder="Enter phone number"
                />
              </Form.Item>

              {/* Postal Code */}
              <Form.Item
                name="postal_code"
                label="Postal Code"
                rules={[
                  { required: true, message: "Please enter postal code" },
                  {
                    pattern: /^[0-9]{6}$/,
                    message: "Postal code must be exactly 6 digits",
                  },
                ]}
              >
                <Input maxLength={6} placeholder="Enter postal code" />
              </Form.Item>

              {/* Address */}
              <Form.Item
                name="address"
                label="Address"
                rules={[{ required: true, message: "Please enter an address" }]}
              >
                <Input.TextArea rows={3} placeholder="Enter address" />
              </Form.Item>

              {/* Latitude */}
              <Form.Item
                name="latitude"
                label="Latitude"
                rules={[{ required: true, message: "Please enter latitude" }]}
              >
                <InputNumber placeholder="Enter latitude" className="w-100" />
              </Form.Item>

              {/* Longitude */}
              <Form.Item
                name="longitude"
                label="Longitude"
                rules={[{ required: true, message: "Please enter longitude" }]}
              >
                <InputNumber placeholder="Enter longitude" className="w-100" />
              </Form.Item>

              {/* Take Away Charge */}
              <Form.Item
                name="take_away_charge"
                label="Take Away Charge"
                rules={[
                  { required: true, message: "Please enter take away charge" },
                ]}
              >
                <InputNumber
                  placeholder="Enter take away charge"
                  className="w-100"
                />
              </Form.Item>

              {/* Tax Percentage */}
              <Form.Item
                name="tax_percentage"
                label="Tax Percentage"
                rules={[
                  { required: true, message: "Please enter tax percentage" },
                ]}
              >
                <InputNumber
                  placeholder="Enter tax percentage"
                  min={0}
                  max={100}
                  className="w-100"
                />
              </Form.Item>

              <Form.Item
                name="third_party_id"
                label="Third Party ID"
                rules={[
                  { required: true, message: "Please enter a third party ID" },
                ]}
              >
                <Input placeholder="Enter third party ID" />
              </Form.Item>

              {/* Coverage Type */}
              <Form.Item
                name="coverage_type"
                label="Coverage Type"
                rules={[
                  { required: true, message: "Please select coverage type" },
                ]}
              >
                <Select>
                  <Option value="Radius">Radius</Option>
                  <Option value="Polygon">Polygon</Option>
                </Select>
              </Form.Item>

              {/* Is Active Checkbox */}
              <Form.Item label={null}>
                <Form.Item name="is_active" valuePropName="checked" noStyle>
                  <Checkbox>Is Active</Checkbox>
                </Form.Item>
              </Form.Item>

              {/* Submit and Cancel Buttons */}
              <Form.Item label={null}>
                <Button
                  type="primary"
                  className="btn-save mr-2"
                  htmlType="submit"
                >
                  {loading ? `${Saving}` : `${Save}`}
                </Button>
                <Button
                  className="btn-cancel"
                  onClick={() => navigate(`/stores/${storeId}/details`)}
                >
                  {Cancel}
                </Button>
              </Form.Item>
            </>
          )}
        </Form>
      </div>
    </div>
  );
};

export default EditStore;
