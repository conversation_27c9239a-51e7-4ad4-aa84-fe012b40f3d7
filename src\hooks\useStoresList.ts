import React, { useEffect, useMemo, useState, useCallback } from "react";
import { Input } from "antd";
import { Store } from "../types";
import { axiosInstance } from "../apiCalls";
import { handleApiError } from "../utils/ApiErrorHandler";

// Generic interface for paginated API responses
interface PaginatedResponse<T> {
  objects: T[];
  total_count: number;
}

// Hook configuration interface
interface UseStoresListConfig {
  endpoint?: string;
  autoFetch?: boolean;
}

// Hook return type
interface UseStoresListReturn {
  stores: Store[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  refetch: () => void;
  clearError: () => void;
}

/**
 * Custom hook for fetching stores list with filtering and pagination
 * @param currentPage - Current page number
 * @param pageSize - Number of items per page
 * @param filters - Filter object for API query parameters
 * @param config - Optional configuration for the hook
 * @returns Object containing stores data, loading state, error state, and utility functions
 */
export const useStoresList = (
  currentPage: number,
  pageSize: number,
  filters: Record<string, string | undefined>,
  config: UseStoresListConfig = {}
): UseStoresListReturn => {
  const {
    endpoint = "/cms/stores/stores/",
    autoFetch = true,
  } = config;

  // State management
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [filters]);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Fetch stores function
  const fetchStores = useCallback(async (signal?: AbortSignal) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get<PaginatedResponse<Store>>(
        endpoint,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
          signal,
        }
      );

      if (response.status === 200) {
        setStores(response.data.objects);
        setTotalCount(response.data.total_count);
      } else {
        setError("Unexpected response format.");
      }
    } catch (error: unknown) {
      handleApiError(error, setError);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, memoizedFilters, endpoint]);

  // Refetch function for manual refresh
  const refetch = useCallback(() => {
    fetchStores();
  }, [fetchStores]);

  // Auto-fetch effect with cleanup
  useEffect(() => {
    if (!autoFetch) return;

    const controller = new AbortController();
    fetchStores(controller.signal);

    return () => {
      controller.abort();
    };
  }, [fetchStores, autoFetch]);

  return {
    stores,
    loading,
    error,
    totalCount,
    refetch,
    clearError,
  };
};

/**
 * Generic hook for fetching any paginated list with filters
 * @param endpoint - API endpoint to fetch data from
 * @param currentPage - Current page number
 * @param pageSize - Number of items per page
 * @param filters - Filter object for API query parameters
 * @param config - Optional configuration for the hook
 * @returns Object containing data, loading state, error state, and utility functions
 */
export const usePaginatedList = <T>(
  endpoint: string,
  currentPage: number,
  pageSize: number,
  filters: Record<string, string | undefined> = {},
  config: { autoFetch?: boolean } = {}
) => {
  const { autoFetch = true } = config;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);

  const memoizedFilters = useMemo(() => filters, [filters]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const fetchData = useCallback(async (signal?: AbortSignal) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get<PaginatedResponse<T>>(
        endpoint,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
          signal,
        }
      );

      if (response.status === 200) {
        setData(response.data.objects);
        setTotalCount(response.data.total_count);
      } else {
        setError("Unexpected response format.");
      }
    } catch (error: unknown) {
      handleApiError(error, setError);
    } finally {
      setLoading(false);
    }
  }, [endpoint, currentPage, pageSize, memoizedFilters]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (!autoFetch) return;

    const controller = new AbortController();
    fetchData(controller.signal);

    return () => {
      controller.abort();
    };
  }, [fetchData, autoFetch]);

  return {
    data,
    loading,
    error,
    totalCount,
    refetch,
    clearError,
  };
};

/**
 * Utility hook for common filter operations
 * @param handleFilterChange - Function to handle filter changes from useTableFilters
 * @returns Object containing common filter utilities
 */
export const useFilterUtils = (
  handleFilterChange: (key: string, value: string) => void
) => {
  // Common text filter dropdown component
  const createTextFilterDropdown = (
    filterKey: string,
    placeholder: string = `Search ${filterKey}`,
    inputRef?: React.RefObject<any>
  ) => ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
  }: any) => (
    <div className="filter-dropdown">
      <Input
        ref={inputRef}
        placeholder={placeholder}
        value={selectedKeys[0]}
        onChange={(e) => {
          const value = e.target.value;
          setSelectedKeys(value ? [value] : []);
          if (!value) {
            clearFilters();
            confirm();
            handleFilterChange(filterKey, "All");
          }
        }}
        onPressEnter={() => {
          confirm();
          handleFilterChange(filterKey, selectedKeys[0]);
        }}
        autoFocus
        suffix={
          selectedKeys[0] ? (
            <span
              onClick={() => {
                setSelectedKeys([]);
                clearFilters();
                confirm();
                handleFilterChange(filterKey, "All");
              }}
              className="search-filter-clear-btn"
            >
              ✖
            </span>
          ) : null
        }
      />
    </div>
  );

  // Text formatter utility
  const textFormat = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  return {
    createTextFilterDropdown,
    textFormat,
  };
};

export default useStoresList;
