import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Typography, Spin, Row, Col, Image, Divider } from "antd";

import dayjs from "dayjs";
import "../../../../../assets/css/Banner/Banner.css";
import { axiosInstance } from "../../../../../apiCalls";
import { CreativeFileObject } from "../../../../../types/CreativeFileType/CreativeFileType";

const { Title, Text } = Typography;

const StoreCreativeFileDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [details, setDetails] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);

  const ASSETS_URL = import.meta.env.VITE_ASSETS_URL;

  const getBannerDetails = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `cms/menu/store-creative-details/${id}/`
      );
      if (response.status === 200) {
        setDetails(response.data);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getBannerDetails();
  }, [id]);

  const formatText = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const renderDetailsInCard = () => {
    if (!details) return null;

    const fields: { label: string; key: keyof CreativeFileObject }[] = [
      { label: "Name", key: "name" },
      { label: "Type", key: "type" },
      { label: "Status", key: "is_active" },

      { label: "Created Date ", key: "created_at" },
      { label: "Updated Date ", key: "updated_at" },
      { label: "Content Type", key: "content_type" },
    ];

    return fields.map((field) => {
      const value = details[field.key];
      let displayValue: React.ReactNode;
      if (
        field.key === "type" ||
        (field.key === "content_type" && typeof value === "string")
      ) {
        displayValue = formatText(value);
      } else if (field.key === "created_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (field.key === "updated_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (typeof value === "boolean") {
        displayValue = value ? "Active" : "Inactive";
      } else if (value === null || value === undefined) {
        displayValue = "-";
      } else if (typeof value === "object") {
        // Display a placeholder message for objects
        displayValue = "Details available";
      } else {
        displayValue = value; // Handle strings and numbers directly
      }

      return (
        <div key={field.key.toString()} className="order-details-value">
          <div className="order-details-label">{field.label}</div>
          <span className="order-details-value-colon">:</span>
          <span className="order-details-value-value">{displayValue}</span>
        </div>
      );
    });
  };

  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  };

  //   const MappedStores = () => {
  //     return (
  //       <div className="section">
  //         <Col span={24} style={{ padding: "20px" }}>
  //           <Title level={4} style={{ marginBottom: "16px", color: "#333" }}>
  //             Stores:
  //           </Title>
  //           {details?.stores?.length ? (
  //             <Row gutter={[16, 16]} justify="start">
  //               {details.stores.map((store: StoreObject) => (
  //                 <Col key={store.id} xs={24} sm={12} md={8} lg={6} xl={4}>
  //                   <Link
  //                     to={`/stores/${store.id}/details`}
  //                     style={{ textDecoration: "none" }}
  //                   >
  //                     <Card
  //                       hoverable
  //                       style={{
  //                         height: "25vh",
  //                         display: "flex",
  //                         justifyContent: "center",
  //                         alignItems: "center",
  //                         textAlign: "center",
  //                         borderRadius: "8px",
  //                         boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
  //                         color: "#FF8732",
  //                       }}
  //                     >
  //                       {store?.store_name} ({store?.store_code})
  //                     </Card>
  //                   </Link>
  //                 </Col>
  //               ))}
  //             </Row>
  //           ) : (
  //             <Empty
  //               description="No Store Mapped Yet"
  //               image={Empty.PRESENTED_IMAGE_SIMPLE}
  //               style={{ marginTop: "20px" }}
  //             />
  //           )}
  //         </Col>
  //       </div>
  //     );
  //   };

  const renderBannerPages = () => {
    if (!details?.source || !details.source.length) return null;

    return (
      <div className="section">
        <Title level={4}>Preview</Title>
        <Row gutter={[16, 16]} justify="start">
          <Col xs={24} sm={12} md={8} lg={6}>
            {details.source ? (
              details.content_type.includes("image") ? (
                <Image
                  src={
                    details.source.startsWith("http")
                      ? details.source
                      : `${ASSETS_URL}/${details.source}`
                  }
                  alt="Banner Page"
                  width={200}
                  style={{ display: "block", margin: "auto" }}
                  preview={true}
                />
              ) : details.content_type.includes("video") ? (
                <video
                  controls
                  src={
                    details.source.startsWith("http")
                      ? details.source
                      : `${ASSETS_URL}/${details.source}`
                  }
                  style={{
                    border: "1px solid #ddd",
                    width: "200px",
                    height: "200px",
                    display: "block",
                    margin: "auto",
                    marginBottom: "10px",
                  }}
                >
                  Your browser does not support the video tag.
                </video>
              ) : details.content_type.includes("text") ? (
                <Typography className="family-Poppins">
                  {formatText(details.source)}
                </Typography>
              ) : null
            ) : (
              <Text type="warning">No source available</Text>
            )}
          </Col>
        </Row>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  }
  if (error) return <Text type="danger">{error}</Text>;
  if (!details) return <Text>No details found</Text>;

  return (
    <>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Creative File Details</div>
        <div className="d-flex">
          {/* <Button type="primary">Edit</Button> */}
        </div>
      </div>

      <div
        className="banner-details-container"
        style={{ display: "flex", gap: "20px", padding: "20px" }}
      >
        {/* Left Side */}
        <div className="left-section" style={{ flex: 1 }}>
          {renderDetailsInCard()}
        </div>

        <Divider className="divider" type="vertical" />

        {/* Right Side */}
        <div className="right-section" style={{ flex: 1 }}>
          {renderBannerPages()}
        </div>
      </div>

      <div>{/* File Selection Modal */}</div>
    </>
  );
};

export default StoreCreativeFileDetails;
