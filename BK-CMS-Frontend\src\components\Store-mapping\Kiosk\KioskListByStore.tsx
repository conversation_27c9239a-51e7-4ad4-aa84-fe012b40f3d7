import { useEffect, useState, useCallback, useMemo } from "react";
import { Button, Form, message, Modal, Alert } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { KioskType } from "../../../types/KioskType/KioskType";
import { axiosInstance } from "../../../apiCalls";
import {
  Add_New_Kiosk,
  Retry,
  STORE_KIOSKS,
} from "../../../constants/Constant";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import { KioskListByStoreProps } from "../../../types/KioskType/KioskListByStoreProps";
import { handleApiError } from "../../../utils/ApiErrorHandler";
import DataTable from "../../shared/DataTable/commonDataTable";
import CommonPagination from "../../shared/Pagination/commonPagination";
import { useTableFilters } from "../../../hooks/useTableFilter";
// The following imports were originally in the dependency array but are not used:
// import { kioskIs_Active_Options, kioskTypes } from "./SelectTypeKiosk/SelectTypeKioksList";

const KioskListByStore = ({ storeId,ato_id,third_party_id,store_code }: KioskListByStoreProps) => {
  const { currentPage, pageSize, handlePageChange } = useTableFilters();
  const [kiosList, setKiosList] = useState<KioskType[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  

  const getKioskListByStore = useCallback(async () => {
    if (!storeId) {
      setError("Store ID is required");
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      const response = await axiosInstance.get(
        `/cms/stores/kiosks/`,
        {
          params: {
            store_code:store_code,
            page: currentPage,
            page_size: pageSize,
          },
        }
      );
      if (response.status === 200) {
        setKiosList(response.data.objects || []);
        setTotalCount(response.data.total_count);
        setError(null);
      } else {
        setError("Failed to fetch Kiosks List");
      }
    } catch (error: unknown) {
      handleApiError(error, setError);
    } finally {
      setLoading(false);
    }
  }, [storeId, currentPage, pageSize]);

  useEffect(() => {
    getKioskListByStore();
  }, [getKioskListByStore]);

  const handleStatusChange = useCallback(
    (kioskId: number, isActive: boolean): void => {
      Modal.confirm({
        title: isActive ? "Activate Kiosk" : "Deactivate Kiosk",
        content: isActive
          ? "Are you sure you want to activate this kiosk?"
          : "Are you sure you want to deactivate this kiosk?",
        okText: "Yes",
        cancelText: "No",
        className: "custom-modal", // Apply global modal styling
        okButtonProps: { className: "custom-modal-ok-button" },
        cancelButtonProps: { className: "custom-modal-cancel-button" },
        async onOk() {
          setLoading(true);
          try {
            const response = await axiosInstance.put(
              `/cms/stores/${storeId}/kiosks/${kioskId}/update/`,
              { kiosk_id: kioskId, is_active: isActive }
            );
            if (response.status === 200) {
              message.success("Kiosk status updated successfully.");

              setKiosList((prevData) =>
                prevData.map((kiosk) =>
                  kiosk.id === kioskId
                    ? { ...kiosk, is_active: isActive }
                    : kiosk
                )
              );
            } else {
              message.error("Failed to update kiosk status.");
            }
          } catch (err: unknown) {
            handleApiError(err, setError);
            message.error("An error occurred while updating kiosk status.");
          } finally {
            setLoading(false);
          }
        },
      });
    },
    [storeId]
  );

  const columns = useMemo(
    () => [
      {
        title: "Code",
        dataIndex: "code",
        key: "code",
        width: "15%",
        fixed: "left" as "left",
        render: (text: string, record: KioskType) =>
          text ? (
            <Link
              className="common-link text-decoration-none"
              to={`/store-kiosks-details/${storeId}/${record.id}`}
            >
              {text}
            </Link>
          ) : (
            "N/A"
          ),
      },
      {
        title: "Token",
        dataIndex: "token",
        key: "token",
        width: "20%",
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        width: "20%",
      },
      // {
      //   title: "EDC Serial Number",
      //   dataIndex: "edc_serial_number",
      //   key: "edc_serial_number",
      //   width: "20%",
      // },
      {
        title: "Printer Serial Number",
        dataIndex: "printer_serial_number",
        key: "printer_serial_number",
        width: "20%",
      },
      {
        title: "Status",
        dataIndex: "is_active",
        key: "is_active",
        width: "20%",
        render: (isActive: boolean, record: KioskType) => (
          <div className="d-flex">
            <div
              className={`switch-button ${isActive ? "checked" : ""}`}
              onClick={() => handleStatusChange(record.id, !isActive)}
            >
              <span className="switch-label">
                {isActive ? <CheckOutlined /> : <CloseOutlined />}
              </span>
              <div className="switch-handle"></div>
            </div>
          </div>
        ),
      },
      {
        title: "",
        key: "actions",
        width: "10%",
        render: (_: unknown, record: KioskType) => (
          <Button
            type="link"
            onClick={() => {
              if (storeId && record.id) {
                navigate(`/stores/${storeId}/kiosks/${record.id}/update/`);
              } else {
                message.error("Invalid store or kiosk ID");
              }
            }}
          >
            <span className="btn-edit-pencil">
              <EditOutlined />
            </span>
          </Button>
        ),
      },
    ],
    [storeId, handleStatusChange, navigate]
  );

  if (!loading && error) {
    <div className="error-container">
      <Alert message="Error" description={error} type="error" showIcon />
      <div className="retry-button d-flex justify-content-center align-items-center mt-3">
        <Button type="primary" onClick={() => getKioskListByStore()}>
          {Retry}
        </Button>
      </div>
    </div>;
  }

  return (
    <div className="mt-3">
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>{STORE_KIOSKS}</div>
        <div className="d-flex">
          <Button
            type="primary"
            onClick={() => navigate(`/stores/${storeId}/${store_code}/${ato_id}/${third_party_id}/add-kiosk`)}
          >
            {Add_New_Kiosk}
          </Button>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <Form component={false}>
          <DataTable<KioskType>
            bordered
            rowKey={(record) =>
              record.id?.toString() ?? record.code ?? `temp-${record.token}`
            }
            dataSource={kiosList}
            columns={columns}
            loading={loading}
            pagination={false}
            scroll={{ x: "max-content" }}
          />
          <CommonPagination
            current={currentPage}
            total={totalCount}
            pageSize={pageSize}
            showSizeChanger={true}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </Form>
      </div>
    </div>
  );
};

export default KioskListByStore;
