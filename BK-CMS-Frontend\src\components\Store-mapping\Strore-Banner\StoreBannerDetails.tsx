import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Card, Typography, Spin, List, Row, Col, Image } from "antd";
import { axiosInstance } from "../../../apiCalls";

import { Object as BannerObject } from "../../../types/BannerType/BannerType";

const { Title, Text } = Typography;

const ASSETS_URL = import.meta.env.VITE_ASSETS_URL;

const StoreBannerDetails: React.FC = () => {
  const params = useParams();
  const id = params.id as string;
  const [details, setDetails] = useState<BannerObject | null>(null);
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);

  const getStoreDetails = async () => {
    try {
      const response = await axiosInstance.get(
        `cms/menu/store-banner-details/${id}/`
      );
      if (response.status === 200) {
        // const fetchedData = response.data.objects;
        setDetails(response.data);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to fetch Store");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getStoreDetails();
  }, [id]);

  if (loading)
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  if (error) return <Text>{error}</Text>;
  if (!details) return <Text>No details found</Text>;

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Store Banner Details</div>
      </div>
      <div className="pt-4 mt-4">
        <List
          grid={{ gutter: 16, column: 1 }}
          dataSource={Array.isArray(details) ? details : [details]}
          renderItem={(item) => (
            <List.Item>
              <Card
                title={"Name :" + item?.name}
                style={{
                  backgroundColor:
                    item?.data?.service_page?.background_color || "#fff",
                }}
              >
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <Title level={4}>Banner</Title>
                    {item?.data?.banner?.map((banner, index) => (
                      <div key={index}>
                        {banner.content_type === "image" && (
                          <Image
                            src={`${ASSETS_URL}/${banner.source}`}
                            alt="Banner"
                            width={200}
                            preview={false}
                          />
                        )}
                      </div>
                    ))}
                  </Col>
                  <Col span={24}>
                    <Title level={4}>Service Page</Title>
                    <Image
                      src={`${ASSETS_URL}/${item?.data?.service_page?.logo}`}
                      alt="Logo"
                      width={100}
                    />
                  </Col>
                  <Col span={24}>
                    <Image
                      src={`${ASSETS_URL}/${item?.data?.service_page?.image_url}`}
                      alt="Logo"
                      width={100}
                    />
                  </Col>

                  <Col span={24}>
                    <Title level={4}>Service Images</Title>
                    <Row gutter={[16, 16]}>
                      {item?.data?.service_page?.service_options?.map(
                        (option) => (
                          <Col key={option?.value} span={8}>
                            <Card
                              cover={
                                <Image
                                  src={`${ASSETS_URL}/${option?.image_url}`}
                                  alt={option?.text}
                                  preview={false}
                                />
                              }
                            >
                              <Card.Meta title={option?.text} />
                            </Card>
                          </Col>
                        )
                      )}
                    </Row>
                  </Col>

                  <Col span={24}>
                    <Title level={4}>Launch Page</Title>
                    {item?.data?.launch_page?.map((launch, index) => (
                      <div key={index}>
                        {launch.source && (
                          <>
                            {/* Check if content_type indicates an image */}
                            {launch.content_type.includes("image") && (
                              <Image
                                src={`${ASSETS_URL}/${launch.source}`}
                                alt="Launch Image"
                                width={200}
                                preview={false}
                              />
                            )}

                            {/* Check if content_type indicates a video */}
                            {launch.content_type.includes("video") && (
                              <video
                                controls
                                src={`${ASSETS_URL}/${launch.source}`}
                                style={{ border: "1px solid #ddd" }}
                              >
                                Your browser does not support the video tag.
                              </video>
                            )}
                          </>
                        )}
                        {!launch.source && (
                          <Text type="warning">
                            No source available for this launch page entry.
                          </Text>
                        )}
                      </div>
                    ))}
                  </Col>
                </Row>
              </Card>
            </List.Item>
          )}
        />
      </div>
    </div>
  );
};

export default StoreBannerDetails;
