import Cookies from "js-cookie";
import axios from "axios";
import {
  ACCESS_TOKEN,
  FIRST_NAME,
  LAST_NAME,
  REFRESH_TOKEN,
  USERNAME,
} from "../constants/Constant";

export const refreshAccessToken = async () => {
  const refreshToken = Cookies.get(REFRESH_TOKEN);

  if (!refreshToken) return null;

  try {
    const response = await axios.post(
      `${import.meta.env.VITE_API_URL}/api/token/refresh/`,
      { refresh: refreshToken },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    if (response.status === 200) {
      console.log("Token refresh response:", response.data);
      const { access, refresh } = response.data;

      Cookies.set(ACCESS_TOKEN, access, { secure: true, sameSite: "Strict" });
      Cookies.set(REFRESH_TOKEN, refresh, { secure: true, sameSite: "Strict" });
      return access;
    }
  } catch (error) {
    console.error("Error refreshing access token:", error);
    Cookies.remove(ACCESS_TOKEN);
    Cookies.remove(REFRESH_TOKEN);
    Cookies.remove(USERNAME);
    Cookies.remove(FIRST_NAME);
    Cookies.remove(LAST_NAME);
    // window.location.pathname = "/";
    return null;
  }
};
