import React from 'react';
import { Container, Navbar, Nav, Button } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import Cookies from 'js-cookie';
import './Topbar.css'; // Ensure you have the styles for the Topbar
import { topBarContent } from '../../constants';

const Topbar: React.FC = () => {
  const navigate = useNavigate();

  const handleLogout = () => {
    Cookies.remove("token");
    navigate("/");
  };

  return (
    <Navbar
      expand="lg"
      bg="dark"
      variant="dark"
      className="shadow-sm custom-navbar"
    >
      <Container>
        <Navbar.Brand
          href="/dashboard"
          style={{
            fontWeight: "bold",
            fontSize: "1.5rem",
            letterSpacing: "1px",
          }}
        >
          CMS
        </Navbar.Brand>
        {/* Button to toggle the collapse menu on small screens */}
        <Navbar.Toggle aria-controls="navbar-nav" />
        {/* Collapsible navbar content */}
        <Navbar.Collapse id="navbar-nav">
          <Nav className="me-auto">
            {topBarContent.map((item) => (
              <Nav.Link
                as={Link}
                to={item.link}
                key={item.id}
                className="nav-item"
              >
                {item.label}
              </Nav.Link>
            ))}
          </Nav>
          <Nav>
            <Button
              variant="danger"
              size="sm"
              onClick={handleLogout}
              className="logout-button"
            >
              Logout
            </Button>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Topbar;
