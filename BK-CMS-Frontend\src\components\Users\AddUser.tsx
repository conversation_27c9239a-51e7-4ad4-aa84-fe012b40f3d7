import React, { useState } from "react";
import { Form, Input, Select, Button, Switch, notification, Space } from "antd";

import { axiosInstance } from "../../apiCalls";
import { useNavigate } from "react-router-dom";
import { Add_User, Cancel, Save } from "../../constants/Constant";
import { AddUserFormValues } from "../../types/UsersType/AddUserType/AddUserType";
import { USER_API_ENDPOINTS } from "../../constants/apiEndpoints";

const AddUser: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleAddUser = async (values: AddUserFormValues) => {
    try {
      setLoading(true);

      const response = await axiosInstance.post(
        USER_API_ENDPOINTS.REGISTER_USER,
        {
          first_name: values.first_name,
          last_name: values.last_name,
          email: values.email,
          user_type: values.user_type,
          username: values.username,
          password: values.password,
          is_active: values.is_active,
          is_staff: true,
          is_superuser: false,
        }
      );

      if (response.status === 201) {
        notification.success({
          message: "Success",
          description: "User added successfully!",
        });
        navigate("/users-list");
      } else {
        notification.error({
          message: "Error",
          description: "Failed to add user. Please try again.",
        });
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred.";

      if (error.response) {
        const errorData = error.response.data;

        // If error response is an object with field-specific errors
        if (typeof errorData === "object" && !Array.isArray(errorData)) {
          errorMessage = Object.entries(errorData)
            .map(([field, messages]) => {
              // Ensure messages is an array of strings
              if (
                Array.isArray(messages) &&
                messages.every((msg) => typeof msg === "string")
              ) {
                return `${field}: ${messages.join(", ")}`;
              }
              return `${field}: ${String(messages)}`;
            })
            .join("\n"); // Formatting error messages
        } else {
          errorMessage = errorData?.message || JSON.stringify(errorData);
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your network.";
      } else {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>{Add_User}</div>
      </div>
      <div className="pt-4 mt-4">
        <Form
          onFinish={handleAddUser}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 14 }}
          className="mt-2"
          style={{ maxWidth: "600px" }}
          autoComplete="off"
          initialValues={{ is_active: true }}
        >
          <Form.Item
            label="First Name"
            name="first_name"
            rules={[{ required: true, message: "Please enter the first name" }]}
          >
            <Input placeholder="Enter first name" />
          </Form.Item>

          <Form.Item
            label="Last Name"
            name="last_name"
            rules={[{ required: true, message: "Please enter the last name" }]}
          >
            <Input placeholder="Enter last name" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="email"
            //   rules={[
            //     { required: true, message: "Please enter the email" },
            //     { type: "email", message: "Please enter a valid email address" },
            //   ]}
          >
            <Input placeholder="Enter email" />
          </Form.Item>

          <Form.Item
            label="User Type"
            name="user_type"
            rules={[{ required: true, message: "Please select user type" }]}
          >
            <Select placeholder="Select user type">
              <Select.Option value={1}>Employee</Select.Option>
              {/* <Select.Option value={2}>Manager</Select.Option>
              <Select.Option value={3}>Admin</Select.Option> */}
            </Select>
          </Form.Item>

          <Form.Item
            label="Username"
            name="username"
            rules={[{ required: true, message: "Please enter the username" }]}
          >
            <Input placeholder="Enter username" />
          </Form.Item>

          <Form.Item
            label="Password"
            name="password"
            rules={[
              { required: true, message: "Please enter the password" },
              {
                min: 8,
                message: "Password must be at least 8 characters long",
              },
            ]}
          >
            <Input.Password
              placeholder="Enter password"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item
            label="Active Status"
            name="is_active"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item label={null}>
            <Space>
              <Button
                type="primary"
                className="btn-save me-4"
                htmlType="submit"
                loading={loading}
              >
                {Save}
              </Button>
              <Button
                type="default"
                className="btn-cancel"
                onClick={() => navigate(-1)}
              >
                {Cancel}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default AddUser;
