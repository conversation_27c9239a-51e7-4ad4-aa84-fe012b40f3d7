.orders-list-container {
  padding: 20px !important;
}
/* Style for the labels */
.order-details-label {
  width: 200px;
  display: inline-block;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 600;
}

.button-back button {
  position: absolute;
  z-index: 9999;
  background: #ff8732;
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Poppins";
}

/* Style for the divider */
.order-details-divider {
  border-color: #7cb305 !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

/* Style for the card */
.order-details-card {
  width: 100%;
  margin-bottom: 16px !important;
}
.table-container {
  position: relative;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1; /* Ensure the loader is above the table */
}

.table-with-loader {
  position: relative;
}

.pagination {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: end;
  align-items: center;
}
.stores-list-header button {
  background: #ff8732 !important;
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
}

/*  */

/* Badge styles */
/* Filter Dropdown Styling */
.filter-dropdown {
  padding: 8px;
}

.select-dropdown {
  width: 100%;
}
.search-filter-clear-btn {
  cursor: pointer;
  margin-left: 10px;
  font-size: 14px;
}

/* Status Wrapper */
.status-wrapper {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 110px;
  text-align: center;
  padding: 2px;
}

/* Status Tag */
.status-tag {
  font-weight: 300;
  font-size: 12px;

  border-radius: 12px;
  /* padding: 4px 12px; */
  background: transparent;
  color: white;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  border: none;
}

/* Status Colors */
.status-wrapper.completed {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
}

.status-wrapper.new {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #fff;
}

.status-wrapper.processing {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
  color: #fff;
}

.status-wrapper.default {
  background: gray;
  color: white;
}
.status-wrapper.paid {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
}

.status-wrapper.unpaid {
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
  color: #fff;
}

.status-wrapper.expired {
  background: linear-gradient(135deg, #d4b106, #fadb14);
  color: #fff;
}

.status-wrapper.refunded {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #fff;
}

.status-wrapper.refund-failed {
  /* Corrected from "Redund Failed" */
  background: linear-gradient(135deg, #ff5722, #ff7043);
  color: #fff;
}

/* Store filter styles */
.filter-dropdown {
  padding: 8px;
}

.filter-input-wrapper {
  position: relative;
}

.store-filter-input {
  width: 100%;
  border: 1px solid #7a6a6a;
}

.clear-icon {
  cursor: pointer;
  color: #999;
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

.clear-icon.hidden {
  opacity: 0;
  pointer-events: none;
}

.filter-options {
  margin-top: 4px;
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  padding: 6px;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: absolute;
  top: 100%;
  left: 0px;
  z-index: 999;
  background-color: #fff;
  border: 1px solid #7a6a6a;
  border-top: none;
}

.filter-option {
  padding: 4px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.filter-option:hover {
  background-color: #f5f5f5;
}

.no-suggestions {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  color: #999;
  height: 50px;
}

.filter-option {
  padding: 6px;
  cursor: pointer;
  border: none;
}

.filter-option.highlighted {
  background-color: #f0f0f0;
}
/* filter btn */
.filter-btn {
  margin-left: 10px;
  border: 1px solid #d9d9d9;
  background: #f5f5f5;
}
/* wrap clear */
.d-flex.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;
}
