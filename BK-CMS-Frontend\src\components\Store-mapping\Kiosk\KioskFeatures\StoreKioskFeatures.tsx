import React, { useState, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { notification, Modal, message } from "antd";

import axios from "axios";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

import dayjs from "dayjs";
import { axiosInstance } from "../../../../apiCalls";
import DataTable from "../../../shared/DataTable/commonDataTable";
import CommonPagination from "../../../shared/Pagination/commonPagination";
import { useTableFilters } from "../../../../hooks/useTableFilter";

const API_ENDPOINT = "/cms/stores/features-kiosk";

export interface KioskFeature {
  id: number;
  feature_name: string;
  feature_code: string;
  kiosk_name: string;
  kiosk_code: string;
  is_active: boolean;
  created_at: string;
  kiosk: number;
  feature: number;
}
export interface KioskFeatureResponse {
  objects: KioskFeature[];
  total_count: number;
}
interface MappedStoreKioskFeaturesProps {
  kioskId: string;
}

const MappedStoreKioskFeatures: React.FC<MappedStoreKioskFeaturesProps> = ({
  kioskId,
}) => {
  //const { id } = useParams<{ id: string }>();
  const { handlePageChange } = useTableFilters();
  const location = useLocation();
  const params = new URLSearchParams(location.search);

  const initialPage = parseInt(params.get("page") || "1", 10);
  const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [storeOptions, setStoreOptions] = useState<KioskFeature[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  //   const updateURLParams = useCallback(
  //     (updatedFilters: Record<string, string | number | undefined>) => {
  //       const params = new URLSearchParams();
  //       Object.entries(updatedFilters).forEach(([key, value]) => {
  //         if (value !== undefined) params.set(key, String(value));
  //       });
  //       navigate(`?${params.toString()}`, { replace: true });
  //     },
  //     [navigate]
  //   );

  const fetchKiosksFeatures = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<KioskFeatureResponse>(
        `${API_ENDPOINT}/?kiosk_id=${kioskId}`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
          },
        }
      );

      if (response.status === 200) {
        setStoreOptions(response.data.objects);
        setTotalCount(response.data.total_count);
      } else {
        setStoreOptions([]);
        //  setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, kioskId]);

  useEffect(() => {
    fetchKiosksFeatures();
  }, []);

  useEffect(() => {
    const params = new URLSearchParams(location.search);

    setCurrentPage(parseInt(params.get("page") || "1", 10));
    setPageSize(parseInt(params.get("page_size") || "10", 10));
  }, [location.search]);

  //   const handlePageChange = (page: number, pageSize?: number) => {
  //     setCurrentPage(page);
  //     setPageSize(pageSize || 10);
  //     updateURLParams({ page, page_size: pageSize || 10 });
  //   };

  const handleStatusChange = (id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Status" : "Deactivate Status",
      content: isActive
        ? "Are you sure you want to activate this Status?"
        : "Are you sure you want to deactivate this Status?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal", // Apply custom class
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          setLoading(true);
          const response = await axiosInstance.patch(
            `/cms/stores/features-kiosk-update/${id}/`,
            {
              is_active: isActive,
            }
          );

          if (response.status === 200) {
            message.success(`Status updated successfully.`);
            setStoreOptions((prevData: any) =>
              prevData.map((feature: any) =>
                feature.id === id
                  ? { ...feature, is_active: isActive }
                  : feature
              )
            );
          } else {
            message.error("Failed to update user status.");
          }
        } catch (err: unknown) {
          if (axios.isAxiosError(err)) {
            message.error(
              err.response?.data?.message ||
                "An error occurred while updating user status."
            );
          } else {
            message.error("An unexpected error occurred.");
          }
        } finally {
          setLoading(false);
        }
      },
    });
  };

  //   const handleAdd = () => {
  //     const feature = storeOptions.length > 0 ? storeOptions[0].feature : null;

  //     if (feature !== null) {
  //       navigate(`/store/add-features-to-kiosk/${kioskId}?feature=${feature}`);
  //     } else {
  //       message.error("No feature found for this kiosk.");
  //     }
  //   };

  const columns = [
    {
      title: "Kiosk Code",
      dataIndex: "kiosk_code",
      key: "kiosk_code",
      width: "10%",
      //   render: (text: string, record: any) =>
      //     text ? (
      //       <Link
      //         className="common-link text-decoration-none"
      //         to={`/stores/${record.store_id}/details`}
      //       >
      //         {text}
      //       </Link>
      //     ) : (
      //       "N/A"
      //     ),
    },
    {
      title: "Kiosk Name",
      width: "20%",
      dataIndex: "kiosk_name",
      key: "kiosk_name",
    },
    {
      title: "Feature Code",
      dataIndex: "feature_code",
      key: "feature_code",
      width: "15%",
    },
    {
      title: "Feature Name",
      dataIndex: "feature_name",
      key: "feature_name",
      width: "20%",
    },
    {
      title: "Date",
      dataIndex: "created_at",
      key: "created_at",
      width: "17%",
      // sorter: (a: Order, b: Order) =>
      //   dayjs(a.created_at).unix() - dayjs(b.created_at).unix(),
      render: (createdAt: string) =>
        dayjs(createdAt).format("DD MMM YYYY, hh:mm: A"), // 12-hour format  //for 24-hour format: dayjs(createdAt).format("DD MMM 'YYYY, HH:mm:ss")
    },

    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      width: "10%",
      fixed: "right" as "right",
      render: (is_active: boolean, record: KioskFeature) => (
        <div className="d-flex">
          <div
            className={`switch-button ${is_active ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !is_active)}
          >
            <span className="switch-label">
              {" "}
              {is_active ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* <div className="d-flex justify-content-end align-items-center mb-4">
        <Button type="primary" className="btn-save" onClick={handleAdd}>
          Add Feature to Kisoks
        </Button>
      </div> */}
      <div className="mt-4">
        <DataTable<KioskFeature>
          columns={columns}
          dataSource={storeOptions}
          loading={loading}
          pagination={false}
          rowKey="id"
        />

        <div className="pagination">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </div>
    </div>
  );
};

export default MappedStoreKioskFeatures;
