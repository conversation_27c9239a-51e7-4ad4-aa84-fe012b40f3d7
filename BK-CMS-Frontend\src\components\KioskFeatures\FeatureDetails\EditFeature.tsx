import React, { useState, useEffect, useCallback } from "react";
import { Form, Input, Button, Card, message, Spin, Switch } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import ErrorFallback from "../../Error/ErrorPage";
import { KioskFeaturesType } from "../KioskFeatures";
import { handleApiError } from "../../../utils/ApiErrorHandler";

const EditFeatures: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeature = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get(
          `/cms/stores/features-details/${id}/`
        );
        if (response.status === 200) {
          form.setFieldsValue(response.data);
        }
      } catch (error) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    if (id) fetchFeature();
  }, [id, form]);

  const handleSubmit = useCallback(
    async (values: KioskFeaturesType) => {
      try {
        setLoading(true);
        const response = await axiosInstance.patch(
          `/cms/stores/features-details/${id}/`,
          values
        );

        if (response.status === 200) {
          message.success(
            response.data.message || "Feature updated successfully"
          );
          navigate(`/feature-details/${id}`);
        }
      } catch (error) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    },
    [navigate, id]
  );

  if (error) {
    return <ErrorFallback error={error} onRetry={() => navigate(-1)} />;
  }

  return (
    <div>
      <h2 className="heading-title">Edit Feature</h2>

      {loading ? (
        <Spin
          className="d-flex justify-content-center items-center m-auto"
          indicator={<LoadingOutlined spin />}
        />
      ) : (
        <Card className="mt-3">
          <Form
            form={form}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 14 }}
            style={{ maxWidth: 600 }}
            onFinish={handleSubmit}
          >
            <Form.Item
              label="Name"
              name="name"
              rules={[{ required: true, message: "Please enter the name" }]}
            >
              <Input placeholder="Enter name" />
            </Form.Item>

            <Form.Item
              label="Code"
              name="code"
              rules={[{ required: true, message: "Please enter the code" }]}
            >
              <Input placeholder="Enter code" />
            </Form.Item>

            <Form.Item
              label="Description"
              name="description"
              rules={[
                { required: true, message: "Please enter the description" },
              ]}
            >
              <Input.TextArea placeholder="Enter description" rows={4} />
            </Form.Item>

            <Form.Item name="is_active" label="Active Status">
              <Switch />
            </Form.Item>

            <Form.Item label={null}>
              <Button
                type="primary"
                htmlType="submit"
                disabled={loading}
                className="btn-save"
              >
                {loading ? <LoadingOutlined spin /> : "Update"}
              </Button>
              <Button
                type="default"
                className="btn-cancel"
                onClick={() => navigate(-1)}
                style={{ marginLeft: 8 }}
              >
                Cancel
              </Button>
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
};

export default EditFeatures;
