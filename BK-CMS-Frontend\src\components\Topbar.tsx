import React from "react";
import Container from "react-bootstrap/Container";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import Cookies from "js-cookie";
import { Link } from "react-router-dom"; // Import Link for navigation
import "./Topbar.css"; // Importing the CSS file
import { topBarContent } from "../constants";
import { axiosInstance } from "../apiCalls";
import { useNavigate } from "react-router-dom";

const Topbar: React.FC = () => {
  const navigate = useNavigate();
  const logout = async () => {
    try {
      const response = await axiosInstance.post("/cms/accounts/logout");

      if (response.status === 200) {
        Cookies.remove("token");
        navigate("/");
      }
    } catch (error) {}
  };
  return (
    <Navbar expand="lg" className="navbar bg-body-tertiary">
      <Container>
        <Navbar.Brand href="/dashboard">CMS</Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            {topBarContent.map((item) => (
              <Nav.Link as={Link} to={item.link} key={item.id}>
                {item.label}
              </Nav.Link>
            ))}
          </Nav>
          <button onClick={logout}>Logout</button>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Topbar;
