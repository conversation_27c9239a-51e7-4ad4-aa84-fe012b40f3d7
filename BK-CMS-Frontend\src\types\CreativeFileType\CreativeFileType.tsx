export interface CreativeFileResponseType {
  objects: CreativeFileObject[];
  total_count: number;
}

export interface CreativeFileObject {
  id: number;
  creative_id: number; //store creative file 
  name: string;
  type: string;
  source: string;
  content_type: string;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

//creative stores
export interface CreativeStoresResponse {
  objects: CreativeStore[];
  total_count: number;
}

export interface CreativeStore {
  key: number;
  id: number;
  store_id: number;
  store_name: string;
  store_code: string;
  is_active: boolean;
}


//creative select stores

// Store interface
export interface CreativeSelectStore {
    id: number;
    name: string;
    code: string;
    is_creative_selected: boolean;
  }
  
  //  API Response type
  export interface CreativeSelectStoresResponse {
    objects: CreativeSelectStore[];
    total_count: number;
  }
