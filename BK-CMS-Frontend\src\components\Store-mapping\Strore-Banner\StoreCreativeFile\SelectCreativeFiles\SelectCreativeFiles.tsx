import { useState, useEffect, useRef, useCallback } from "react";
import {
  Mo<PERSON>,
  Button,
  notification,
  Row,
  Col,
  Card,
  Image,
  Typography,
  Select,
} from "antd";
import { CheckCircleOutlined, CloseOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../../../../apiCalls";
import { handleApiError } from "../../../../../utils/ApiErrorHandler";
import "../../../../../assets/css/Banner/Banner.css";
import { CreativeFileObject } from "../../../../../types/CreativeFileType/CreativeFileType";
import { Cancel, Save } from "../../../../../constants/Constant";

const { Option } = Select;
const VITE_ASSETS_URL = import.meta.env.VITE_ASSETS_URL;

const SelectCreativeFileModal = ({
  visible,
  dynamicAttributes,
  onClose,
  storeId,
  refreshData,
}: {
  visible: boolean;
  dynamicAttributes: any;
  onClose: () => void;
  storeId: string;
  refreshData: () => void;
}) => {
  const [existingFiles, setExistingFiles] = useState<CreativeFileObject[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string>("");

  const [scrollLoading, setScrollLoading] = useState(false);

  const pageSize = 10;
  const [totalCount, setTotalCount] = useState(0);
  const isFetchingRef = useRef(false);
  const modalBodyRef = useRef<HTMLDivElement | null>(null);

  // Fetch Creative Files
  const fetchExistingFiles = useCallback(
    async (page = 1, reset = false) => {
      if (isFetchingRef.current) return;
      isFetchingRef.current = true;
      setLoading(true);
      setError(null);
      setScrollLoading(true);

      try {
        const response = await axiosInstance.get(
          `/cms/menu/creatives_files/?type=${fileType}&page=${page}&page_size=${pageSize}`
        );
        if (response.status === 200) {
          setExistingFiles((prevFiles) =>
            reset
              ? response.data.objects
              : [...prevFiles, ...response.data.objects]
          );
          setTotalCount(response.data.total_count);
        }
      } catch (error) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
        setScrollLoading(false);
        isFetchingRef.current = false;
      }
    },
    [fileType]
  );

  useEffect(() => {
    if (visible) {
      setExistingFiles([]);
      fetchExistingFiles(1, true);
    }
  }, [fileType, visible, fetchExistingFiles]);

  // Updated Scroll Handler without unnecessary state update
  const handleScroll = useCallback(() => {
    const modalBody = modalBodyRef.current;
    if (!modalBody || loading || existingFiles.length >= totalCount) return;

    const { scrollTop, scrollHeight, clientHeight } = modalBody;
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      setScrollLoading(true);
      fetchExistingFiles(existingFiles.length / pageSize + 1);
      setScrollLoading(false);
    }
  }, [loading, existingFiles.length, totalCount, fetchExistingFiles]); //

  useEffect(() => {
    const modalBody = modalBodyRef.current;
    if (!modalBody) return;

    modalBody.addEventListener("scroll", handleScroll);
    return () => {
      modalBody.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  const handleFileSelect = (file: CreativeFileObject) => {
    setSelectedFiles((prevSelected) => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(file.id)) {
        newSelected.delete(file.id);
      } else {
        newSelected.clear();
        newSelected.add(file.id);
      }
      return Array.from(newSelected);
    });
  };

  const handleCancelModal = () => {
    setSelectedFiles([]);
    setFileType("");
    setScrollLoading(false);
    setLoading(false);
    setError(null);
    onClose();
  };

  const handleSelectFileType = (value: string) => {
    setFileType(value);
  };

  const handleSubmit = async () => {
    if (selectedFiles.length === 0) {
      notification.warning({ message: "Please select a file" });
      return;
    }
    try {
      await axiosInstance.post(`cms/menu/store-creatives/`, {
        creative_id: selectedFiles,
        store_id: [Number(storeId)],
      });
      notification.success({ message: "Files mapped successfully" });
      refreshData();
      handleCancelModal();
    } catch (error) {
      notification.error({ message: "Failed to map files" });
    }
  };

  const formatText = (text: string) => {
    if (!text && text === "") return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        document.getElementById("my-modal")?.classList.add("show");
      }, 0);
    }
  }, [visible]);

  return (
    <Modal
      title="Select Creative Files"
      open={visible}
      maskClosable={false}
      onCancel={handleCancelModal}
      footer={[
        <Button
          key="save"
          className="btn-save"
          type="primary"
          onClick={handleSubmit}
        >
          {Save}
        </Button>,
        <Button key="cancel" className="btn-cancel" onClick={handleCancelModal}>
          {Cancel}
        </Button>,
      ]}
    >
      <div ref={modalBodyRef} className="modal-custom-scroll">
        {error && <p style={{ color: "red" }}>{error}</p>}

        <div className="d-flex align-items-center mb-4">
          <Select
            style={{ width: 200 }}
            placeholder="Select File Type"
            value={fileType || undefined}
            onChange={handleSelectFileType}
          >
            {dynamicAttributes.creative_types.map((type: any) => (
              <Option key={type} value={type}>
                {formatText(type)}
              </Option>
            ))}
          </Select>

          {fileType && (
            <Button onClick={() => setFileType("")} type="default">
              {formatText(fileType)} <CloseOutlined />
            </Button>
          )}
        </div>

        <Row gutter={[16, 16]}>
          {existingFiles.length === 0 && !loading ? (
            <Col span={24}>
              <Typography.Text
                type="secondary"
                className="text-center d-block mt-3"
              >
                No data found
              </Typography.Text>
            </Col>
          ) : (
            existingFiles.map((file) => (
              <Col key={file.id} xs={12} sm={4} md={6}>
                <Card
                  hoverable
                  onClick={() => handleFileSelect(file)}
                  style={{
                    border: selectedFiles.includes(file.id)
                      ? "2px solid #ff8732"
                      : "none",
                    position: "relative",
                  }}
                  cover={
                    file.content_type.startsWith("image") ? (
                      <Image
                        src={`${VITE_ASSETS_URL}/${file.source}`}
                        alt="File Preview"
                        style={{ height: 100, objectFit: "cover" }}
                      />
                    ) : file.content_type.startsWith("video") ? (
                      <video
                        src={`${VITE_ASSETS_URL}/${file.source}`}
                        controls
                        style={{ height: 100, objectFit: "cover" }}
                      />
                    ) : (
                      <div className="card-type-text">
                        <Typography className="family-Poppins">
                          {file.source}
                        </Typography>
                      </div>
                    )
                  }
                >
                  <Card.Meta title={file.content_type} />
                  {selectedFiles.includes(file.id) && (
                    <CheckCircleOutlined className="check-icon" />
                  )}
                </Card>
              </Col>
            ))
          )}
        </Row>

        {/* Loading Indicator */}
        {scrollLoading && (
          <Typography className="text-center mt-3">
            Loading more files...
          </Typography>
        )}
      </div>
    </Modal>
  );
};

export default SelectCreativeFileModal;
