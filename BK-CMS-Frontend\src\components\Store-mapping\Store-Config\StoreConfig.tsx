import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON>, Spin, Divider, Button, Row, Col, Card } from "antd";

import { Store } from "../../../types";
import { axiosInstance } from "../../../apiCalls";
import { KioskListByStoreProps } from "../../../types/KioskType/KioskListByStoreProps";

const StoreConfig = ({ storeId, store_cofig }: KioskListByStoreProps) => {
  const { id } = useParams<{ id: string }>(); // Get the store ID from the URL
  const [stores, setStores] = useState<Store | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Fetch the store details based on the ID
  const fetchStoreDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(
        `cms/stores/store-details/${storeId}/`
      );
      if (response.status === 200) {
        setStores(response.data);
      } else {
        setError("Failed to load store details.");
      }
    } catch (error) {
      console.error("Error fetching store details:", error);
      setError("Failed to load store details.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStoreDetails();
  }, [id]);

  if (loading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "100vh" }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message={error} type="error" />;
  }

  if (!stores) {
    return <Alert message="Store not found." type="warning" />;
  }

  // Safely access `payment_config` and `tax_config` with optional chaining
  const paymentConfig = stores.store_config_data?.payment_config;
  const taxConfig = stores.store_config_data?.tax_config;

  return (
    <div>
      <Divider
        style={{ borderColor: "#7cb305", fontSize: "20px", fontWeight: "bold" }}
      >
        Store-Config
      </Divider>

      <div className="d-flex justify-content-end align-items-right mb-3">
        <Button
          type="link"
          onClick={() =>
            navigate(
              `/cms/stores/store-config-update/${storeId}/${store_cofig}/`
            )
          }
        >
          Edit
        </Button>
      </div>

      {/* Payment Config Card */}
      <Row gutter={10}>
        <Col span={12}>
          <Card title="Payment Config" bordered={true}>
            <Row>
              <Col span={12}>
                <strong>QR:</strong>
              </Col>
              <Col>{paymentConfig?.qr ? "Enable" : "Disable"}</Col>
            </Row>
            <Row>
              <Col span={12}>
                <strong>Card:</strong>
              </Col>
              <Col>{paymentConfig?.card ? "Enable" : "Disable"}</Col>
            </Row>
            <Row>
              <Col span={12}>
                <strong>Cash:</strong>
              </Col>
              <Col>{paymentConfig?.cash ? "Enable" : "Disable"}</Col>
            </Row>
          </Card>
        </Col>

        {/* Tax Config Card */}
        <Col span={12}>
          <Card title="Tax Config" bordered={true}>
            <Row>
              <Col span={12}>
                <strong>CGST:</strong>
              </Col>
              <Col>
                {taxConfig?.cgst !== undefined
                  ? `${taxConfig.cgst}%`
                  : "Not Set"}
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <strong>SGST:</strong>
              </Col>
              <Col>
                {taxConfig?.sgst !== undefined
                  ? `${taxConfig.sgst}%`
                  : "Not Set"}
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default StoreConfig;
