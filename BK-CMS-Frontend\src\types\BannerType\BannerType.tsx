export interface PresignedUrlFields {
  key: string;
  policy: string;
  "x-amz-date": string;
  "x-amz-algorithm": string;
  "x-amz-signature": string;
  "x-amz-credential": string;
}

export interface PresignedUrl {
  url: string;
  fields: PresignedUrlFields;
}

export interface Banner {
  source: string;
  file_key: string;
  content_type: string;
  presigned_url: PresignedUrl;
}

export interface LaunchPage {
  source: string;
  file_key: string;
  content_type: string;
  presigned_url: PresignedUrl;
}

export interface ServiceOption {
  text: string;
  value: string;
  image_url: string;
}

export interface ServicePage {
  logo: string;
  image_url: string;
  welcome_text: string;
  logo_file_key: string;
  image_file_key: string;
  service_options: ServiceOption[];
  background_color: string;
}

export interface Data {
  banner?: Banner[];
  launch_page?: LaunchPage[];
  service_page?: ServicePage;
}

export interface Stores {
  id: number;
  name: string;
}

export interface Object {
  id: number;
  banner_id: number;
  store_id: number;
  stores: Stores[];
  name: string | null;
  data: Data;
  is_active: boolean;
  business: string | null;
  created_at: string;
  updated_at: string;
}

export interface ResponseData {
  objects: Object[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: string | null;
  previous_page: string | null;
  total_count: number;
}
