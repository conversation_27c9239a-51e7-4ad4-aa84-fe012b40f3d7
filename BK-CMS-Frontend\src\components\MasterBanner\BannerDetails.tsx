import React, { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import {
  Card,
  Typography,
  Spin,
  List,
  Row,
  Col,
  Image,
  Empty,
  Divider,
} from "antd";

import { Object as BannerObject } from "../../types/BannerType/BannerType";
import { axiosInstance } from "../../apiCalls";
import dayjs from "dayjs";
import "../../assets/css/Banner/Banner.css";

const { Title, Text } = Typography;

const ASSETS_URL = import.meta.env.VITE_ASSETS_URL;

const BannerDetails: React.FC = () => {
  const params = useParams();
  const id = params.id as string;
  const [details, setDetails] = useState<BannerObject | null>(null);
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);

  const getStoreDetails = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `cms/menu/banner-details/${id}/`
      );
      if (response.status === 200) {
        setDetails(response.data);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getStoreDetails();
  }, [id]);

  const renderDetailsInCard = () => {
    if (!details) return null;

    const fields: { label: string; key: keyof BannerObject }[] = [
      { label: "Banner Name", key: "name" },
      { label: "Created Date ", key: "created_at" },
      { label: "Updated Date ", key: "updated_at" },
    ];

    return fields.map((field) => {
      const value = details[field.key];
      let displayValue: React.ReactNode;

      if (field.key === "created_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (field.key === "updated_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (typeof value === "boolean") {
        displayValue = value ? "Yes" : "No";
      } else if (value === null || value === undefined) {
        displayValue = "-";
      } else if (typeof value === "object") {
        // Display a placeholder message for objects
        displayValue = "Details available";
      } else {
        displayValue = value; // Handle strings and numbers directly
      }

      return (
        <div
          key={field.key}
          className="order-details-value"
          style={{ marginBottom: "10px" }}
        >
          <div className="order-details-label" style={{ fontWeight: "bold" }}>
            {field.label}
          </div>
          <span
            className="order-details-value-colon"
            style={{ margin: "0 5px" }}
          >
            :
          </span>
          <span>{displayValue}</span>
        </div>
      );
    });
  };

  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  };

  if (loading)
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  if (error) return <Text>{error}</Text>;
  if (!details) return <Text>No details found</Text>;

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Banner Details</div>
      </div>
      <div className="pt-4 mt-4">
        <div className="order-details-card" style={{ marginBottom: "30px" }}>
          {renderDetailsInCard()}
        </div>
        <List
          grid={{ gutter: 16, column: 1 }}
          dataSource={Array.isArray(details) ? details : [details]}
          renderItem={(item) => (
            <List.Item>
              <Card

              // style={{
              //   backgroundColor:
              //     item?.data?.service_page?.background_color || "#fff",
              // }}
              >
                <Col span={24} style={{ padding: "20px" }}>
                  <Title
                    level={4}
                    style={{ marginBottom: "16px", color: "#333" }}
                  >
                    Stores:
                  </Title>
                  {item?.stores && item?.stores?.length > 0 ? (
                    <Row gutter={[16, 16]} justify="start">
                      {item?.stores.map((store) => (
                        <Col
                          key={store.id}
                          xs={24}
                          sm={12}
                          md={8}
                          lg={6}
                          xl={4}
                        >
                          <Link
                            to={`/stores/${store.id}/details`}
                            style={{ textDecoration: "none" }}
                          >
                            <Card
                              hoverable
                              style={{
                                height: "60px", // Ensuring equal height for all cards
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                textAlign: "center",
                                borderRadius: "8px",
                                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                                color: "#FF8732",
                              }}
                            >
                              {store.name}
                            </Card>
                          </Link>
                        </Col>
                      ))}
                    </Row>
                  ) : (
                    <Empty
                      description="No Store Mapped Yet"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      style={{ marginTop: "20px" }}
                    />
                  )}
                </Col>
                <div className="mt-4">
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <Title level={4}>Banner</Title>
                      {item?.data?.banner?.map((banner, index) => (
                        <div className="banner-image" key={index}>
                          {banner.content_type === "image" && (
                            <Image
                              src={`${ASSETS_URL}/${banner.source}`}
                              alt="Banner"
                              width={150}
                              height={100}
                              preview={true}
                            />
                          )}
                        </div>
                      ))}
                    </Col>
                    <Col span={24}>
                      <Title level={4}>Service Page Logo</Title>
                      <div className="banner-image">
                        <Image
                          src={`${ASSETS_URL}/${item?.data?.service_page?.logo}`}
                          alt="Logo"
                          width={150}
                          height={100}
                        />
                      </div>
                    </Col>
                    <Col span={24}>
                      <Title level={4}>Service Page Image</Title>
                      <div className="banner-image">
                        <Image
                          src={`${ASSETS_URL}/${item?.data?.service_page?.image_url}`}
                          alt="Logo"
                          width={150}
                          height={100}
                        />
                      </div>
                    </Col>

                    <Col span={24}>
                      <Title level={4}>Service Images</Title>
                      <Row gutter={[16, 16]}>
                        {item?.data?.service_page?.service_options?.map(
                          (option) => (
                            <Col
                              key={option?.value}
                              span={2}
                              style={{
                                width: "150px",

                                position: "relative",
                                marginBottom: "80px",
                                marginLeft: "142px",
                              }}
                            >
                              <div>
                                <Image
                                  src={`${ASSETS_URL}/${option?.image_url}`}
                                  alt={option?.text}
                                  preview={true}
                                  style={{
                                    width: 150,
                                    height: 150,
                                  }}
                                />

                                <div
                                  style={{
                                    fontSize: "14px",
                                    fontFamily: "Poppins, sans-serif",
                                    width: "4rem",
                                  }}
                                >
                                  {option?.text}
                                </div>
                              </div>
                            </Col>
                          )
                        )}
                      </Row>
                    </Col>
                    <Divider className="divider" />
                    <Col span={24}>
                      <Title level={4}>Launch Pages</Title>
                      <Row gutter={[16, 16]} justify="start">
                        {item?.data?.launch_page?.map((launch, index) => (
                          <Col
                            key={index}
                            span={6}
                            style={{ marginLeft: "134px" }}
                          >
                            {" "}
                            {/* Adjust span based on desired width */}
                            {launch.source ? (
                              <>
                                {/* Check if content_type is an image */}
                                {launch.content_type.includes("image") && (
                                  <Image
                                    src={`${ASSETS_URL}/${launch.source}`}
                                    alt="Launch Image"
                                    width={200}
                                    preview={true}
                                    style={{
                                      display: "block",
                                      margin: "auto",
                                    }}
                                  />
                                )}

                                {/* Check if content_type is a video */}
                                {launch.content_type.includes("video") && (
                                  <video
                                    controls
                                    src={`${ASSETS_URL}/${launch.source}`}
                                    style={{
                                      border: "1px solid #ddd",
                                      width: "200px",
                                      height: "200px",
                                      display: "block",
                                      margin: "auto",
                                      marginBottom: "10px",
                                    }}
                                  >
                                    Your browser does not support the video tag.
                                  </video>
                                )}
                              </>
                            ) : (
                              <Text type="warning">
                                No source available for this launch page entry.
                              </Text>
                            )}
                          </Col>
                        ))}
                      </Row>
                    </Col>
                  </Row>
                </div>
              </Card>
            </List.Item>
          )}
        />
      </div>
    </div>
  );
};

export default BannerDetails;
