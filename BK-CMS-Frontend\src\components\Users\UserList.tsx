import React, { useState, useEffect, useMemo, useRef } from "react";
import { Button, message, Modal } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { ColumnsType } from "antd/es/table";
import axios from "axios";
import { axiosInstance } from "../../apiCalls";
import { UserType } from "../../types/UsersType/UserType";
import {
  Add_New_User_Btn,
  PAGINATION_DEFAULTS,
  User_List,
} from "../../constants/Constant";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { USER_API_ENDPOINTS } from "../../constants/apiEndpoints";
import { MESSAGES } from "../../constants/messages/messages";
import { handleApiError } from "../../utils/ApiErrorHandler";
import ErrorFallback from "../Error/ErrorPage";
import DataTable from "../shared/DataTable/commonDataTable";
import CommonPagination from "../shared/Pagination/commonPagination";

interface UserTypeObject {
  objects: UserType[];
  total_count: number;
}

const UserList: React.FC = () => {
  const [data, setData] = useState<UserType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);

  const location = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(location.search);

  const initialPage =
    Number(searchParams.get("page")) || PAGINATION_DEFAULTS.PAGE;
  const initialPageSize =
    Number(searchParams.get("page_size")) || PAGINATION_DEFAULTS.PAGE_SIZE;

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [filters, setFilters] = useState<Record<string, string | undefined>>(
    () => {
      const filterObj: Record<string, string | undefined> = {};
      searchParams.forEach((value, key) => {
        if (!["page", "page_size"].includes(key)) {
          filterObj[key] = value;
        }
      });
      return filterObj;
    }
  );

  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    abortControllerRef.current?.abort(); // Abort previous API call if exists
    abortControllerRef.current = new AbortController();
    fetchUsers(
      currentPage,
      pageSize,
      filters,
      abortControllerRef.current.signal
    );

    return () => {
      abortControllerRef.current?.abort();
    };
  }, [currentPage, pageSize, filters]);

  const fetchUsers = async (
    page: number,
    size: number,
    filters: Record<string, string | undefined>,
    signal: AbortSignal
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get<UserTypeObject>(
        USER_API_ENDPOINTS.GET_USER_LIST,
        {
          params: { page, page_size: size, ...filters },
          signal, // Pass abort signal
        }
      );

      if (response.status === 200) {
        setData(response.data.objects);
        setTotalCount(response.data.total_count);
      } else {
        setError(MESSAGES.FETCH_ERROR);
      }
    } catch (err: unknown) {
      handleApiError(err, setError);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);

    const updatedFilters = {
      ...filters,
      page: page.toString(),
      page_size: size?.toString() || pageSize.toString(),
    };
    setFilters(updatedFilters);
    updateURLParams(updatedFilters);
  };

  const updateURLParams = (
    updatedFilters: Record<string, string | undefined>
  ) => {
    const params = new URLSearchParams();
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value) params.set(key, value);
    });
    navigate(`?${params.toString()}`);
  };

  const handleStatusChange = (userId: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate User" : "Deactivate User",
      content: isActive
        ? "Are you sure you want to activate this user?"
        : "Are you sure you want to deactivate this user?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal", // Apply custom class
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          setLoading(true);
          const response = await axiosInstance.patch(
            USER_API_ENDPOINTS.ENABLE_DISABLE_USER,
            {
              user_id: userId,
              is_active: isActive,
            }
          );

          if (response.status === 200) {
            message.success("User status updated successfully.");
            setData((prevData) =>
              prevData.map((user) =>
                user.id === userId ? { ...user, is_active: isActive } : user
              )
            );
          } else {
            message.error("Failed to update user status.");
          }
        } catch (err: unknown) {
          if (axios.isAxiosError(err)) {
            message.error(
              err.response?.data?.message ||
                "An error occurred while updating user status."
            );
          } else {
            message.error("An unexpected error occurred.");
          }
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const columns: ColumnsType<UserType> = useMemo(
    () => [
      {
        title: "Username",
        dataIndex: "username",
        key: "username",
        width: "15%",
      },
      {
        title: "First Name",
        dataIndex: "first_name",
        key: "first_name",
        width: "15%",
      },
      {
        title: "Last Name",
        dataIndex: "last_name",
        key: "last_name",
        width: "15%",
      },
      {
        title: "Email",
        dataIndex: "email",
        key: "email",
        width: "20%",
      },
      {
        title: "Date Joined",
        dataIndex: "date_joined",
        key: "date_joined",
        width: "15%",
        render: (date: string) => new Date(date).toLocaleDateString(),
      },
      {
        title: "User Status",
        dataIndex: "is_active",
        key: "user_status",
        width: "10%",
        render: (isActive: boolean, record: UserType) => (
          <div className="d-flex">
            <div
              className={`switch-button ${isActive ? "checked" : ""}`}
              onClick={() => handleStatusChange(record.id, !isActive)}
            >
              <span className="switch-label">
                {isActive ? <CheckOutlined /> : <CloseOutlined />}
              </span>
              <div className="switch-handle"></div>
            </div>
          </div>
        ),
      },
    ],
    [handleStatusChange]
  );

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>{User_List}</div>
        <Button
          type="primary"
          className="add-store"
          onClick={() => navigate(`/register-user`)}
        >
          {Add_New_User_Btn}
        </Button>
      </div>
      <div className="pt-4 mt-4">
        <DataTable<UserType>
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={false}
          loading={loading}
        />
      </div>
      <div className="pagination">
        <CommonPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          showSizeChanger
          onShowSizeChange={handlePageChange}
          onChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default UserList;
