import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON>, message, Modal } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { ColumnsType } from "antd/es/table";
import axios from "axios";
import { CheckOutlined, CloseOutlined, PlusOutlined } from "@ant-design/icons";

import dayjs from "dayjs";
import { useTableFilters } from "../../hooks/useTableFilter";
import { axiosInstance } from "../../apiCalls";
import { handleApiError } from "../../utils/ApiErrorHandler";
import ErrorFallback from "../Error/ErrorPage";

import DataTable from "../shared/DataTable/commonDataTable";

export interface KioskFeaturesType {
  id: number;
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  created_at: string;
}
// interface KioskFeaturesRespone {
//   objects: KioskFeaturesType[];
//   total_count: number;
// }

const KioskFeatures: React.FC = () => {
  const { currentPage, pageSize, filters } = useTableFilters();
  //   const { metaData, errors } = useMetaData();
  const [data, setData] = useState<KioskFeaturesType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  //const [totalCount, setTotalCount] = useState<number>(0);

  const navigate = useNavigate();

  //   const [dynamicAttributes, setDynamicAttributes] = useState({
  //     creative_types: [] as string[], // Ensure it's always an array
  //   });

  // const abortControllerRef = useRef<AbortController | null>(null);
  const memoizedFilters = useMemo(() => filters, [filters]);

  //   useEffect(() => {
  //     const creatives = metaData?.creative_meta;
  //     if (creatives) {
  //       setDynamicAttributes(creatives);
  //     }
  //   }, [metaData, errors]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchCreatives = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<KioskFeaturesType>(
          "/cms/stores/features/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200 && Array.isArray(response.data)) {
          setData(response.data);
          // setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchCreatives();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  const handleStatusChange = (id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Status" : "Deactivate Status",
      content: isActive
        ? "Are you sure you want to activate this Status?"
        : "Are you sure you want to deactivate this Status?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal", // Apply custom class
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          setLoading(true);
          const response = await axiosInstance.patch(
            `/cms/stores/features-details/${id}/`,
            {
              is_active: isActive,
            }
          );

          if (response.status === 200) {
            message.success(`Status updated successfully.`);
            setData((prevData: any) =>
              prevData.map((feature: any) =>
                feature.id === id
                  ? { ...feature, is_active: isActive }
                  : feature
              )
            );
          } else {
            message.error("Failed to update user status.");
          }
        } catch (err: unknown) {
          if (axios.isAxiosError(err)) {
            message.error(
              err.response?.data?.message ||
                "An error occurred while updating user status."
            );
          } else {
            message.error("An unexpected error occurred.");
          }
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const columns: ColumnsType<KioskFeaturesType> = useMemo(
    () => [
      {
        title: "Code",
        dataIndex: "code",
        key: "code",

        render: (code: string, record: KioskFeaturesType) => (
          <Link
            className="common-link text-decoration-none"
            to={`/feature-details/${record.id}`}
          >
            {code}
          </Link>
        ),
      },
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
      },
      //   {
      //     title: "Description",
      //     dataIndex: "description",
      //     key: "description",
      //   },
      {
        title: "Created At",
        dataIndex: "created_at",
        key: "created_at",

        render: (created: string) =>
          dayjs(created).format("DD MMM YYYY, hh:mm A"),
      },
      //   {
      //     title: "Updated At",
      //     dataIndex: "updated_at",
      //     key: "updated_at",
      //     render: (updated: string) =>
      //       dayjs(updated).format("DD MMM YYYY, hh:mm A"),
      //   },
      {
        title: "Status",
        dataIndex: "is_active",
        key: "status",
        render: (is_active: boolean, record: KioskFeaturesType) => (
          <div className="d-flex">
            <div
              className={`switch-button ${is_active ? "checked" : ""}`}
              onClick={() => handleStatusChange(record.id, !is_active)}
            >
              <span className="switch-label">
                {" "}
                {is_active ? <CheckOutlined /> : <CloseOutlined />}
              </span>
              <div className="switch-handle"></div>
            </div>
          </div>
        ),
      },
    ],
    [handleStatusChange]
  );

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Kiosk Features List</div>
        <Button
          type="primary"
          className="add-store"
          onClick={() => navigate(`/add-kiosk-features`)}
        >
          <PlusOutlined />
          Add New Feature
        </Button>
      </div>
      <div className="pt-2 mt-2">
        <DataTable<KioskFeaturesType>
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={false}
          loading={loading}
          // scroll={{ x: 1000 }}
        />
      </div>
      {/*<CommonPagination
        current={currentPage}
        pageSize={pageSize}
        total={totalCount}
        showSizeChanger
        onShowSizeChange={handlePageChange}
        onChange={handlePageChange}
      />
    </div> */}
    </div>
  );
};

export default KioskFeatures;
