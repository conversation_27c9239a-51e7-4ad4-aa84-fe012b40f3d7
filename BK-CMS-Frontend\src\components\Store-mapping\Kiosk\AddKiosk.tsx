

import { Form, Input, Select, Switch, Button, message } from "antd"
import { useNavigate, useParams } from "react-router-dom"
import { Add_Kiosk, Cancel, Save, Saving } from "../../../constants/Constant"
import { useState } from "react"
import { kioskTypes, STORE_TYPE_CHOICES } from "./SelectTypeKiosk/SelectTypeKioksList"
import type { KioskType } from "../../../types/KioskType/KioskType"
import { axiosInstance } from "../../../apiCalls"

const AddKiosk = () => {
  const [selectedKiosk, setSelectedKiosk] = useState<string | undefined>(undefined)
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  // const allParams = useParams()
  // console.log("[v0] All available params:", allParams)

  const { storeId, storeCode, ato_id, third_party_id } = useParams<{
    storeId: string
    storeCode: string
    ato_id: string
    third_party_id: string
  }>()

  const handleAddKiosk = async (values: KioskType) => {
    try {
      setLoading(true)
      // console.log("[v0] storeId:", storeId)
      // console.log("[v0] store_code:", storeCode)
      // console.log("[v0] ato_id:", ato_id)
      // console.log("[v0] third_party_id:", third_party_id)

      const payload = {
        ...values,
        ato_id: ato_id,
        store_uid: third_party_id,
        store_code: storeCode,
      }
      console.log(payload)

      const response = await axiosInstance.post(
        `/cms/stores/kiosks/`,
        payload
      );

      if (response.status === 201) {
        message.success("Kiosk added successfully");
        navigate(`/stores/${storeId}/kiosk`);
      } else {
        message.error("Failed to add kiosk");
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred."

      if (error.response) {
        const errorData = error.response.data

        if (typeof errorData === "object" && !Array.isArray(errorData)) {
          errorMessage = Object.entries(errorData)
            .map(([field, messages]) => {
              if (Array.isArray(messages) && messages.every((msg) => typeof msg === "string")) {
                return `${field}: ${messages.join(", ")}`
              }
              return `${field}: ${String(messages)}`
            })
            .join("\n") // Formatting error messages
        } else {
          errorMessage = errorData?.message || JSON.stringify(errorData)
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your network."
      } else {
        errorMessage = error.message
      }
      message.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container bg-white p-3 rounded-3">
      <div className="heading-title">{Add_Kiosk}</div>
      <div className="pt-3">
        <Form
          name="basic"
          onFinish={handleAddKiosk}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600, marginTop: 20 }}
          initialValues={{ is_active: false }}
          autoComplete="off"
        >
          {/* Code Field */}
          <Form.Item name="code" label="Code" rules={[{ required: true, message: "Please input the kiosk code!" }]}>
            <Input placeholder="Enter kiosk code" />
          </Form.Item>

          {/* Name Field */}
          <Form.Item name="name" label="Name" rules={[{ required: true, message: "Please input the kiosk name!" }]}>
            <Input placeholder="Enter kiosk name" />
          </Form.Item>

          {/* Version Field */}
          <Form.Item
            name="version"
            label="Version"
            rules={[
              { required: true, message: "Please input the version!" },
              {
                validator(_, value) {
                  if (value === undefined || value >= 0) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error("Version cannot be less than 0!"))
                },
              },
            ]}
          >
            <Input
              type="number"
              placeholder="Enter version"
              onInput={(e) => {
                const target = e.target as HTMLInputElement
                if (Number(target.value) < 0) {
                  target.value = "0" // Reset the value to 0 if less than 0 is entered
                }
              }}
              onKeyDown={(e) => {
                if (e.key === "-" || e.key === "e" || e.key === "E") {
                  e.preventDefault() // Prevent negative and exponential entries
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="edc_store_id"
            label="EDC Store ID"
            rules={[{ required: true, message: "Please input the EDC Store ID!" }]}
          >
            <Input type="number" placeholder="Enter EDC Store ID" />
          </Form.Item>

          <Form.Item
            name="edc_clientiD"
            label="EDC Client ID"
            rules={[{ required: true, message: "Please input the EDC Client ID!" }]}
          >
            <Input type="number" placeholder="Enter EDC Client ID" />
          </Form.Item>

          <Form.Item
            name="edc_securitytoken"
            label="EDC Security Token"
            rules={[{ required: true, message: "Please input the EDC Security Token!" }]}
          >
            <Input type="text" placeholder="Enter EDC Security Token" />
          </Form.Item>

          {/* TeamViewer ID Field */}
          <Form.Item
            name="teamviewer_id"
            label="TeamViewer ID"
            rules={[{ required: true, message: "Please input the TeamViewer ID!" }]}
          >
            <Input placeholder="Enter TeamViewer ID" />
          </Form.Item>

          {/* AnyDesk ID Field */}
          <Form.Item
            name="anydesk_id"
            label="AnyDesk ID"
            rules={[{ required: true, message: "Please input the AnyDesk ID!" }]}
          >
            <Input placeholder="Enter AnyDesk ID" />
          </Form.Item>

          {/* EDC Serial Number Field
          <Form.Item
            name="edc_serial_number"
            label="EDC Serial Number"
            rules={[{ required: true, message: "Please input the EDC Serial Number!" }]}
          >
            <Input placeholder="Enter EDC Serial Number" />
          </Form.Item> */}

          {/* Printer Serial Number Field */}
          <Form.Item
            name="printer_serial_number"
            label="Printer Serial Number"
            rules={[{ required: true, message: "Please input the Printer Serial Number!" }]}
          >
            <Input placeholder="Enter Printer Serial Number" />
          </Form.Item>

          {/* Type Field */}
          <Form.Item name="type" label="Type" rules={[{ required: true, message: "Please select the kiosk type!" }]}>
            <Select
              placeholder="Select kiosk type"
              value={selectedKiosk}
              onChange={(value: string) => setSelectedKiosk(value)}
            >
              {kioskTypes.map((kiosk) => (
                <Select.Option key={kiosk.value} value={kiosk.value}>
                  {kiosk.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="store_location_type"
            label="Store Location Type"
            rules={[{ required: true, message: "Please select the store location type!" }]}
          >
            <Select
              placeholder="Select store location type"
              value={selectedKiosk}
              onChange={(value: string) => setSelectedKiosk(value)}
            >
              {STORE_TYPE_CHOICES.map((kiosk) => (
                <Select.Option key={kiosk.value} value={kiosk.value}>
                  {kiosk.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Is Active Field */}
          <Form.Item name="is_active" label="Is Active" valuePropName="checked">
            <Switch />
          </Form.Item>

          {/* Submit and Cancel Buttons */}
          <Form.Item label={null}>
            <Button className="btn-save" type="primary" htmlType="submit">
              {loading ? `${Saving}` : `${Save}`}
            </Button>
            <Button className="btn-cancel" onClick={() => navigate(`/stores/${storeId}/kiosk`)}>
              {Cancel}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  )
}

export default AddKiosk
