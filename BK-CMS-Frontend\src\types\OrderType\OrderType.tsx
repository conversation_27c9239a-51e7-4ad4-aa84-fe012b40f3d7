// Order List
export interface OrderType {
  key: string;
  id: number;
  table_number: number | null;
  order_type: string;
  order_status: "completed" | string;
  notes: string;
  payment_method: "upi_qr" | string;
  payment_status: "paid" | "unpaid" | string;
  payment_id: string | null;
  donation_enabled: boolean;
  grand_total: number | null;
  taxable_value: number | null;
  discount_value: number;
  rounding: number;
  pos_order_number: string | null;
  queue_number: number | null;
  kiosk_terminal_id: number | null;
  created_at: string; // ISO 8601 format
  customer: number;
  customer_name: string;
  customer_phone: string;
  store: string;
  store_id: number;
  synced: boolean;
  product_details: ProductDetails;
}

export interface ModifierGroup {
  [key: string]: {
    name: string;
    modifier: number;
    quantity: number;
  };
}

interface Section {
  name: string;
  modifier_groups: Record<string, Record<string, ModifierGroup>>;
  child_variant_code: string;
}
// interface Modifier {
//   name: string;
//   quantity: number;
// }

export interface Item {
  id: number;
  uid: string;
  code: string;
  name: string;
  quantity: number;
  sections: {
    choose_side?: Section;
    choose_drink?: Section;
  };
  sub_total: number;
}

export interface ProductDetails {
  cgst: number;
  sgst: number;
  items: {
    [key: string]: Item;
  };
  total: number;
  sub_total: number;
}

export interface ProcessedProduct {
  name: string;

  modifiers: ModifierGroup[];
  quantity: number;
  subTotal: number;
}

export interface Order {
  id: string;
  store_id: string;
  customer_id: string;
  payment_id: string;
  store: string;
  customer: string;
  customer_mobile: string;
  order_type: string;
  order_status: string;
  payment_status: string;
  grand_total: number;
  created_at: string;
}

export interface OrdersResponse {
  objects: Order[];
  total_count: number;
}